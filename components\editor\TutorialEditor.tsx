'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import { useState, useCallback, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Bold, 
  Italic, 
  Strikethrough, 
  Code, 
  List, 
  ListOrdered, 
  Quote, 
  Undo, 
  Redo,
  Image as ImageIcon,
  Link as LinkIcon,
  Code2
} from 'lucide-react'

// ==========================================
// TipTap富文本编辑器组件
// 支持Markdown、图片、链接、代码块
// ==========================================

interface TutorialEditorProps {
  initialContent?: string
  onSave?: (content: string) => void
  onAutoSave?: (content: string) => void
  mediaUploadUrl?: string
  className?: string
  placeholder?: string
  editable?: boolean
}

export function TutorialEditor({
  initialContent = '',
  onSave,
  onAutoSave,
  mediaUploadUrl = '/api/admin/media/upload',
  className = '',
  placeholder = '开始编写你的教程内容...',
  editable = true
}: TutorialEditorProps) {
  const [isSaving, setIsSaving] = useState(false)
  
  const editor = useEditor({
    extensions: [
      StarterKit, // 使用默认的codeBlock
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
    ],
    content: initialContent,
    editable,
    immediatelyRender: false, // 修复SSR水合不匹配问题
    onUpdate: ({ editor }) => {
      const content = editor.getHTML()
      // 自动保存机制
      if (onAutoSave) {
        const autoSaveTimer = setTimeout(() => {
          onAutoSave(content)
        }, 2000) // 2秒后自动保存
        
        return () => clearTimeout(autoSaveTimer)
      }
    },
  })

  // 手动保存
  const handleSave = useCallback(async () => {
    if (!editor || !onSave) return
    
    setIsSaving(true)
    try {
      const content = editor.getHTML()
      await onSave(content)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }, [editor, onSave])

  // 插入图片 - 支持上传
  const insertImage = useCallback(async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        // 创建FormData上传文件
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('/api/admin/media', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success && editor) {
            editor.chain().focus().setImage({ src: result.data.url }).run()
          }
        } else {
          // 如果上传失败，允许用户输入URL
          const url = window.prompt('上传失败，请输入图片URL:')
          if (url && editor) {
            editor.chain().focus().setImage({ src: url }).run()
          }
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        // 如果上传失败，允许用户输入URL
        const url = window.prompt('上传失败，请输入图片URL:')
        if (url && editor) {
          editor.chain().focus().setImage({ src: url }).run()
        }
      }
    }
    input.click()
  }, [editor])

  // 插入链接
  const insertLink = useCallback(() => {
    const url = window.prompt('请输入链接URL:')
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }, [editor])

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault()
            handleSave()
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleSave])

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-gray-500">编辑器加载中...</div>
      </div>
    )
  }

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center gap-1 p-2 bg-gray-50 border-b border-gray-200 flex-wrap">
        {/* 基础格式化 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-gray-200' : ''}
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-gray-200' : ''}
        >
          <Italic className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'bg-gray-200' : ''}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={editor.isActive('code') ? 'bg-gray-200' : ''}
        >
          <Code className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 列表 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
        >
          <List className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 引用和代码块 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
        >
          <Quote className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'bg-gray-200' : ''}
        >
          <Code2 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 媒体插入 */}
        <Button variant="ghost" size="sm" onClick={insertImage}>
          <ImageIcon className="h-4 w-4" />
        </Button>
        
        <Button variant="ghost" size="sm" onClick={insertLink}>
          <LinkIcon className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 撤销重做 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>

        {/* 保存按钮 */}
        {onSave && (
          <>
            <Separator orientation="vertical" className="h-6 mx-1" />
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? '保存中...' : '保存 (Ctrl+S)'}
            </Button>
          </>
        )}
      </div>

      {/* 编辑器内容区域 */}
      <div className="prose prose-sm max-w-none">
        <EditorContent 
          editor={editor} 
          className="min-h-[400px] p-4 focus:outline-none"
          placeholder={placeholder}
        />
      </div>

      {/* 底部状态栏 */}
      <div className="flex justify-between items-center p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
        <div>
          字符数: {editor.storage.characterCount?.characters() || 0} | 
          单词数: {editor.storage.characterCount?.words() || 0}
        </div>
        <div>
          支持Markdown语法 | Ctrl+S保存
        </div>
      </div>
    </div>
  )
}