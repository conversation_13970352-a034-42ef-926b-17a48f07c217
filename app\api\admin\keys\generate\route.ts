import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { generateBatchKeys, generateUniqueKey } from "@/lib/key-generator"

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// 验证密钥在数据库中的唯一性
async function verifyKeyUniqueness(key: string): Promise<boolean> {
  const { data, error } = await supabaseAdmin
    .from('tutorial_keys')
    .select('id')
    .eq('key_code', key)
    .limit(1)

  if (error) {
    console.error("Key uniqueness check error:", error)
    return false
  }

  return !data || data.length === 0
}

// 生成确保唯一的密钥
async function generateUniqueKeyWithDbCheck(): Promise<string> {
  let attempts = 0
  const maxAttempts = 10

  while (attempts < maxAttempts) {
    const key = generateUniqueKey()
    const isUnique = await verifyKeyUniqueness(key)
    
    if (isUnique) {
      return key
    }
    
    attempts++
  }

  throw new Error("Failed to generate unique key after multiple attempts")
}

// 批量生成确保唯一的密钥
async function generateUniqueBatchKeys(count: number): Promise<string[]> {
  const keys: string[] = []
  let attempts = 0
  const maxAttempts = count * 3 // 给足够的重试机会

  while (keys.length < count && attempts < maxAttempts) {
    try {
      const key = await generateUniqueKeyWithDbCheck()
      keys.push(key)
    } catch (error) {
      console.error("Error generating unique key:", error)
      attempts++
    }
  }

  if (keys.length < count) {
    throw new Error(`Only generated ${keys.length} unique keys out of ${count} requested`)
  }

  return keys
}

export async function POST(request: NextRequest) {
  try {
    const { tutorial_id, count } = await request.json()

    if (!tutorial_id || !count || count < 1 || count > 100) {
      return NextResponse.json({ error: "Invalid parameters (count: 1-100)" }, { status: 400 })
    }

    // 验证教程是否存在并且已发布
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title, status')
      .eq('id', tutorial_id)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 })
    }

    if (tutorial.status !== 'published') {
      return NextResponse.json({ error: "Tutorial is not published" }, { status: 400 })
    }

    // 生成确保唯一的密钥
    const keys = await generateUniqueBatchKeys(count)

    // 批量插入密钥到数据库
    const keyData = keys.map(key => ({
      tutorial_id: tutorial_id,
      key_code: key,
      status: 'unused',
      created_at: new Date().toISOString()
    }))

    const { data, error: insertError } = await supabaseAdmin
      .from('tutorial_keys')
      .insert(keyData)
      .select()

    if (insertError) {
      console.error("Insert keys error:", insertError)
      
      // 如果是唯一性约束错误，尝试重新生成
      if (insertError.code === '23505') {
        return NextResponse.json({ 
          error: "Key uniqueness conflict detected. Please try again." 
        }, { status: 409 })
      }
      
      return NextResponse.json({ error: "Failed to insert keys" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        generated_count: keys.length,
        tutorial_title: tutorial.title,
        generation_time: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error("Generate keys error:", error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate keys" 
    }, { status: 500 })
  }
}
