import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { getUserIdentifier } from "@/lib/auth"

// ==========================================
// 用户解锁记录API v2 - 架构师重构版本
// 支持统计分析、分页、性能优化
// ==========================================

interface UserUnlockStats {
  total_unlocks: number
  total_spent: number
  categories_unlocked: string[]
  last_unlock_date: string | null
  learning_streak: number
}

interface UnlockRecord {
  id: number
  title: string
  description: string
  content: string
  price: number
  tags: string[]
  status: string
  created_at: string
  category: {
    id: number
    name: string
    description: string
  }
  category_name: string
  unlocked_at: string
  key_used: string | null
  progress?: {
    last_accessed: string | null
    reading_time: number
  }
}

interface APIResponse<T> {
  success: boolean
  data: T
  stats?: UserUnlockStats
  pagination?: {
    page: number
    limit: number
    total: number
    hasNext: boolean
    hasPrev: boolean
  }
  error?: string
}

function parseQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  return {
    user_identifier: searchParams.get('user_identifier'),
    tutorial_id: searchParams.get('tutorialId'), // 添加教程ID参数
    page: Math.max(1, parseInt(searchParams.get('page') || '1')),
    limit: Math.min(20, Math.max(1, parseInt(searchParams.get('limit') || '10'))),
    include_stats: searchParams.get('include_stats') === 'true',
    include_content: searchParams.get('include_content') === 'true'
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('📚 用户解锁记录API查询开始')
    
    const params = parseQueryParams(request)
    
    // 🔧 特殊处理：如果只查询特定教程的解锁统计
    if (params.tutorial_id && !params.user_identifier) {
      console.log('📊 查询教程解锁统计:', params.tutorial_id)
      
      const { data: unlocks, error } = await supabaseAdmin
        .from('user_unlocks')
        .select('user_identifier, unlocked_at')
        .eq('tutorial_id', parseInt(params.tutorial_id))
      
      if (error) {
        console.error('❌ 教程解锁统计查询错误:', error)
        throw error
      }
      
      console.log(`✅ 教程 ${params.tutorial_id} 有 ${unlocks?.length || 0} 个解锁记录`)
      
      return NextResponse.json({
        success: true,
        data: unlocks || []
      })
    }
    
    // 获取用户标识符
    const userIdentifier = params.user_identifier || getUserIdentifier(request)
    console.log('👤 查询用户:', userIdentifier)

    // 构建基础查询
    let baseQuery = supabaseAdmin.from('user_unlocks')
    
    // 如果指定了教程ID，则过滤特定教程
    if (params.tutorial_id) {
      baseQuery = baseQuery.eq('tutorial_id', parseInt(params.tutorial_id))
    }

    const selectFields = `
      unlocked_at,
      key_id,
      tutorials!inner (
        id,
        title,
        description,
        ${params.include_content ? 'content,' : ''}
        price,
        tags,
        status,
        created_at,
        categories!inner(
          id,
          name,
          description
        )
      ),
      tutorial_keys!inner(key_code)
    `

    // 获取总数（用于分页）
    const { count: totalCount, error: countError } = await baseQuery
      .select('*', { count: 'exact', head: true })
      .eq('user_identifier', userIdentifier)

    if (countError) {
      console.error('❌ 统计查询错误:', countError)
      throw countError
    }

    // 获取分页数据
    const offset = (params.page - 1) * params.limit
    const { data: unlocks, error } = await baseQuery
      .select(selectFields)
      .eq('user_identifier', userIdentifier)
      .order('unlocked_at', { ascending: false })
      .range(offset, offset + params.limit - 1)

    if (error) {
      console.error('❌ 用户解锁记录查询错误:', error)
      throw error
    }

    // 格式化解锁记录
    const formattedUnlocks: UnlockRecord[] = unlocks?.map(unlock => ({
      id: unlock.tutorials.id,
      title: unlock.tutorials.title,
      description: unlock.tutorials.description,
      content: unlock.tutorials.content || '',
      price: unlock.tutorials.price,
      tags: unlock.tutorials.tags || [],
      status: unlock.tutorials.status,
      created_at: unlock.tutorials.created_at,
      category: {
        id: unlock.tutorials.categories.id,
        name: unlock.tutorials.categories.name,
        description: unlock.tutorials.categories.description
      },
      category_name: unlock.tutorials.categories.name,
      unlocked_at: unlock.unlocked_at,
      key_used: unlock.tutorial_keys?.key_code || null,
      // 预留进度字段，后续实现
      progress: {
        last_accessed: null,
        reading_time: 0
      }
    })) || []

    // 计算用户统计信息
    let userStats: UserUnlockStats | undefined
    if (params.include_stats && unlocks && unlocks.length > 0) {
      const allUserUnlocks = await supabaseAdmin
        .from('user_unlocks')
        .select(`
          unlocked_at,
          tutorials!inner(price, categories!inner(name))
        `)
        .eq('user_identifier', userIdentifier)

      if (allUserUnlocks.data) {
        const totalSpent = allUserUnlocks.data.reduce((sum, unlock) => 
          sum + (unlock.tutorials.price || 0), 0)
        
        const categoriesSet = new Set(
          allUserUnlocks.data.map(unlock => unlock.tutorials.categories.name)
        )

        const sortedDates = allUserUnlocks.data
          .map(unlock => new Date(unlock.unlocked_at))
          .sort((a, b) => b.getTime() - a.getTime())

        userStats = {
          total_unlocks: allUserUnlocks.data.length,
          total_spent: totalSpent,
          categories_unlocked: Array.from(categoriesSet),
          last_unlock_date: sortedDates[0]?.toISOString() || null,
          learning_streak: 1 // 简化实现，后续可基于解锁时间计算
        }
      }
    }

    // 分页信息
    const total = totalCount || 0
    const totalPages = Math.ceil(total / params.limit)
    const pagination = {
      page: params.page,
      limit: params.limit,
      total,
      hasNext: params.page < totalPages,
      hasPrev: params.page > 1
    }

    // 标准化API响应
    const response: APIResponse<UnlockRecord[]> = {
      success: true,
      data: formattedUnlocks,
      pagination,
      ...(userStats && { stats: userStats })
    }

    console.log(`✅ 返回用户 ${userIdentifier} 的 ${formattedUnlocks.length}/${total} 条解锁记录`)

    // 设置缓存头（用户相关数据缓存时间较短）
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'private, max-age=60, stale-while-revalidate=120',
        'X-User-Unlocks': total.toString(),
        'X-Page': params.page.toString()
      }
    })

  } catch (error) {
    console.error('❌ 用户解锁记录API异常:', error)
    
    const errorResponse: APIResponse<null> = {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : '获取用户解锁记录失败'
    }

    return NextResponse.json(errorResponse, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
