import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// 前端客户端 (用于用户操作) - 受 RLS 策略限制
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  }
})

// 服务端客户端 (用于 API 路由和管理员操作) - 绕过 RLS
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    db: {
      schema: 'public'
    }
  }
)

// 用户身份验证辅助函数
export async function getAuthenticatedUser() {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('User not authenticated')
  }
  return user
}

// 检查用户是否为管理员
export async function isAdmin(userId: string): Promise<boolean> {
  try {
    // 这里可以实现更复杂的角色检查逻辑
    // 目前简化为检查特定的管理员用户ID或邮箱
    const { data: { user } } = await supabase.auth.getUser()
    return user?.email === '<EMAIL>' // 临时实现
  } catch {
    return false
  }
}