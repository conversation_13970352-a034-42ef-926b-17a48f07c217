-- ==========================================
-- 添加测试密钥数据 (PostgreSQL语法)
-- ==========================================

-- 为每个教程生成测试密钥
INSERT INTO tutorial_keys (tutorial_id, key_code, status, expiry_date) VALUES
-- Next.js 教程的密钥
(1, 'NEXTJS-2024-ABC123', 'unused', CURRENT_DATE + INTERVAL '30 days'),
(1, 'NEXTJS-2024-DEF456', 'unused', CURRENT_DATE + INTERVAL '30 days'),
(1, 'NEXTJS-2024-GHI789', 'used', CURRENT_DATE + INTERVAL '30 days'),

-- UI/UX 设计教程的密钥
(2, 'DESIGN-2024-JKL012', 'unused', CURRENT_DATE + INTERVAL '30 days'),
(2, 'DESIGN-2024-MNO345', 'unused', CURRENT_DATE + INTERVAL '30 days'),
(2, 'DESIGN-2024-PQR678', 'unused', CURRENT_DATE + INTERVAL '30 days')
ON CONFLICT DO NOTHING;

-- 更新管理员密码为哈希版本
UPDATE system_config 
SET config_value = '$2b$12$AxqzFq5qb4DYCd/fH7IemOk6EU00/WmTnSzdzMCBlQkzpJ4MTv1ri',
    description = '管理员登录密码哈希值 - bcrypt加密'
WHERE config_key = 'admin_password';

-- 查看插入的密钥
SELECT 
    tk.key_code,
    tk.status,
    t.title as tutorial_title,
    tk.expiry_date,
    tk.created_at
FROM tutorial_keys tk
JOIN tutorials t ON tk.tutorial_id = t.id
ORDER BY tk.created_at DESC;