import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { getUserIdentifier } from "@/lib/auth"

// ==========================================
// 学习进度跟踪API
// 支持进度记录、统计查询、成就解锁
// ==========================================

interface LearningProgressRequest {
  tutorialId: number
  sectionId?: number
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'paused'
  progressPercentage: number
  timeSpent: number
  interactionData?: {
    scrollPercentage?: number
    interactionCount?: number
    pauseCount?: number
    deviceType?: string
  }
}

interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  achievements?: any[]
}

/**
 * 记录学习进度
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📚 开始记录学习进度')
    
    const userIdentifier = getUserIdentifier(request)
    const progressData: LearningProgressRequest = await request.json()
    
    console.log('👤 用户:', userIdentifier)
    console.log('📊 进度数据:', progressData)

    // 生成学习会话ID
    const sessionId = `session_${userIdentifier}_${Date.now()}`

    // 准备学习记录数据
    const learningRecord = {
      user_identifier: userIdentifier,
      tutorial_id: progressData.tutorialId,
      section_id: progressData.sectionId,
      status: progressData.status,
      progress_percentage: progressData.progressPercentage,
      total_time_spent: progressData.timeSpent,
      active_time_spent: progressData.timeSpent, // 简化实现
      scroll_percentage: progressData.interactionData?.scrollPercentage || 0,
      interaction_count: progressData.interactionData?.interactionCount || 0,
      pause_count: progressData.interactionData?.pauseCount || 0,
      device_type: progressData.interactionData?.deviceType || 'unknown',
      learning_session_id: sessionId,
      last_accessed_at: new Date().toISOString(),
      started_at: progressData.status !== 'not_started' ? new Date().toISOString() : null,
      completed_at: progressData.status === 'completed' ? new Date().toISOString() : null
    }

    // 插入或更新学习记录
    const { data: record, error: recordError } = await supabaseAdmin
      .from('user_learning_records')
      .upsert(learningRecord, {
        onConflict: 'user_identifier,tutorial_id,section_id'
      })
      .select()
      .single()

    if (recordError) {
      console.error('❌ 学习记录保存失败:', recordError)
      throw recordError
    }

    console.log('✅ 学习记录保存成功')

    // 更新用户学习统计
    const { error: statsError } = await supabaseAdmin
      .rpc('update_user_learning_stats', {
        p_user_identifier: userIdentifier
      })

    if (statsError) {
      console.warn('⚠️ 统计更新失败:', statsError)
    }

    // 检查成就解锁
    const unlockedAchievements = await checkAchievements(userIdentifier)

    const response: APIResponse<typeof record> = {
      success: true,
      data: record,
      achievements: unlockedAchievements
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ 学习进度记录异常:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '学习进度记录失败'
    }, { status: 500 })
  }
}

/**
 * 获取学习统计
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userIdentifier = searchParams.get('user') || getUserIdentifier(request)
    const tutorialId = searchParams.get('tutorialId')

    console.log('📊 查询学习统计:', { userIdentifier, tutorialId })

    // 获取用户整体学习统计
    const { data: userStats, error: statsError } = await supabaseAdmin
      .from('user_learning_stats')
      .select('*')
      .eq('user_identifier', userIdentifier)
      .single()

    if (statsError && statsError.code !== 'PGRST116') {
      console.error('❌ 统计查询失败:', statsError)
      throw statsError
    }

    // 获取具体教程的学习记录
    let tutorialProgress = null
    if (tutorialId) {
      const { data: progressData, error: progressError } = await supabaseAdmin
        .from('user_learning_records')
        .select(`
          *,
          tutorial_sections (
            id, title, order_index, estimated_reading_time
          )
        `)
        .eq('user_identifier', userIdentifier)
        .eq('tutorial_id', parseInt(tutorialId))
        .order('created_at', { ascending: false })

      if (progressError) {
        console.error('❌ 教程进度查询失败:', progressError)
      } else {
        tutorialProgress = progressData
      }
    }

    // 获取最近获得的成就
    const { data: recentAchievements, error: achievementsError } = await supabaseAdmin
      .from('user_achievements')
      .select(`
        *,
        learning_achievements (
          display_name, description, category, points, badge_color, rarity
        )
      `)
      .eq('user_identifier', userIdentifier)
      .order('unlocked_at', { ascending: false })
      .limit(5)

    if (achievementsError) {
      console.warn('⚠️ 成就查询失败:', achievementsError)
    }

    const response = {
      success: true,
      data: {
        userStats: userStats || {
          user_identifier: userIdentifier,
          total_tutorials_unlocked: 0,
          total_tutorials_started: 0,
          total_tutorials_completed: 0,
          total_learning_time: 0,
          completion_rate: 0
        },
        tutorialProgress: tutorialProgress || [],
        recentAchievements: recentAchievements || []
      }
    }

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'private, max-age=30'
      }
    })

  } catch (error) {
    console.error('❌ 学习统计查询异常:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '学习统计查询失败'
    }, { status: 500 })
  }
}

/**
 * 检查用户成就解锁
 */
async function checkAchievements(userIdentifier: string) {
  try {
    // 获取用户当前统计
    const { data: stats } = await supabaseAdmin
      .from('user_learning_stats')
      .select('*')
      .eq('user_identifier', userIdentifier)
      .single()

    if (!stats) return []

    // 获取所有活跃成就
    const { data: achievements } = await supabaseAdmin
      .from('learning_achievements')
      .select('*')
      .eq('is_active', true)

    if (!achievements) return []

    // 获取用户已解锁的成就
    const { data: userAchievements } = await supabaseAdmin
      .from('user_achievements')
      .select('achievement_id')
      .eq('user_identifier', userIdentifier)

    const unlockedIds = new Set(userAchievements?.map(ua => ua.achievement_id) || [])
    const newUnlocks = []

    // 检查每个成就的解锁条件
    for (const achievement of achievements) {
      if (unlockedIds.has(achievement.id)) continue

      const conditions = achievement.unlock_conditions
      let shouldUnlock = false

      // 简化的条件检查逻辑
      if (conditions.tutorials_unlocked && stats.total_tutorials_unlocked >= conditions.tutorials_unlocked) {
        shouldUnlock = true
      } else if (conditions.tutorials_completed && stats.total_tutorials_completed >= conditions.tutorials_completed) {
        shouldUnlock = true
      } else if (conditions.learning_streak_days && stats.learning_streak_days >= conditions.learning_streak_days) {
        shouldUnlock = true
      }

      if (shouldUnlock) {
        // 解锁成就
        const { error } = await supabaseAdmin
          .from('user_achievements')
          .insert({
            user_identifier: userIdentifier,
            achievement_id: achievement.id,
            progress_data: stats
          })

        if (!error) {
          newUnlocks.push({
            id: achievement.id,
            display_name: achievement.display_name,
            description: achievement.description,
            points: achievement.points,
            badge_color: achievement.badge_color,
            rarity: achievement.rarity
          })
          console.log(`🏆 用户 ${userIdentifier} 解锁成就: ${achievement.display_name}`)
        }
      }
    }

    return newUnlocks

  } catch (error) {
    console.error('❌ 成就检查失败:', error)
    return []
  }
}