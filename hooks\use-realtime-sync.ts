/**
 * 实时数据同步Hook - 基于Supabase Realtime
 * 提供可选的实时数据更新功能
 */

import { useState, useEffect, useCallback } from 'react'
import { supabaseAdmin } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'

interface RealtimeSyncOptions {
  table: string
  filter?: string
  enabled?: boolean
  onInsert?: (payload: any) => void
  onUpdate?: (payload: any) => void
  onDelete?: (payload: any) => void
  onError?: (error: Error) => void
}

interface RealtimeSyncResult {
  connected: boolean
  lastEvent: any
  eventCount: number
  disconnect: () => void
  reconnect: () => void
}

/**
 * Supabase实时数据同步Hook
 * 
 * 注意：这是可选功能，需要Supabase项目启用Realtime
 */
export function useRealtimeSync(options: RealtimeSyncOptions): RealtimeSyncResult {
  const {
    table,
    filter,
    enabled = false,
    onInsert,
    onUpdate,
    onDelete,
    onError
  } = options

  const [connected, setConnected] = useState(false)
  const [lastEvent, setLastEvent] = useState<any>(null)
  const [eventCount, setEventCount] = useState(0)
  const [subscription, setSubscription] = useState<any>(null)
  const { toast } = useToast()

  const connect = useCallback(() => {
    if (!enabled || !table) return

    try {
      console.log(`🔗 启动实时同步: ${table}`)

      let query = supabaseAdmin
        .channel(`${table}_changes`)
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: table,
            filter: filter
          }, 
          (payload) => {
            console.log(`📡 实时事件 [${table}]:`, payload)
            
            setLastEvent(payload)
            setEventCount(prev => prev + 1)
            
            // 触发相应的回调
            switch (payload.eventType) {
              case 'INSERT':
                if (onInsert) {
                  onInsert(payload)
                }
                // 显示新教程通知
                if (table === 'tutorials' && payload.new?.status === 'published') {
                  toast({
                    title: "新教程发布！",
                    description: `${payload.new.title} 现在可以学习了`,
                    duration: 5000
                  })
                }
                break
                
              case 'UPDATE':
                if (onUpdate) {
                  onUpdate(payload)
                }
                break
                
              case 'DELETE':
                if (onDelete) {
                  onDelete(payload)
                }
                break
            }
          }
        )
        .subscribe((status) => {
          console.log(`📡 实时同步状态 [${table}]:`, status)
          
          if (status === 'SUBSCRIBED') {
            setConnected(true)
            console.log(`✅ 实时同步已连接: ${table}`)
          } else if (status === 'CLOSED') {
            setConnected(false)
            console.log(`❌ 实时同步已断开: ${table}`)
          }
        })

      setSubscription(query)

    } catch (error) {
      console.error(`❌ 实时同步连接失败 [${table}]:`, error)
      setConnected(false)
      
      if (onError) {
        onError(error instanceof Error ? error : new Error('Realtime connection failed'))
      }
    }
  }, [table, filter, enabled, onInsert, onUpdate, onDelete, onError, toast])

  const disconnect = useCallback(() => {
    if (subscription) {
      console.log(`🔌 断开实时同步: ${table}`)
      supabaseAdmin.removeChannel(subscription)
      setSubscription(null)
      setConnected(false)
    }
  }, [subscription, table])

  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(() => {
      connect()
    }, 1000)
  }, [disconnect, connect])

  // 自动连接和清理
  useEffect(() => {
    if (enabled) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // 网络状态监听
  useEffect(() => {
    const handleOnline = () => {
      if (enabled && !connected) {
        console.log('🌐 网络恢复，重新连接实时同步')
        reconnect()
      }
    }

    const handleOffline = () => {
      console.log('📴 网络断开，实时同步暂停')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [enabled, connected, reconnect])

  return {
    connected,
    lastEvent,
    eventCount,
    disconnect,
    reconnect
  }
}

/**
 * 教程实时同步Hook
 */
export function useTutorialsRealtime(enabled = false) {
  return useRealtimeSync({
    table: 'tutorials',
    filter: 'status=eq.published', // 只监听已发布的教程
    enabled,
    onInsert: (payload) => {
      console.log('🆕 新教程发布:', payload.new)
      
      // 触发全局缓存刷新事件
      window.dispatchEvent(new CustomEvent('realtime-tutorial-insert', {
        detail: { tutorial: payload.new }
      }))
    },
    onUpdate: (payload) => {
      console.log('📝 教程更新:', payload.new)
      
      // 触发全局缓存刷新事件
      window.dispatchEvent(new CustomEvent('realtime-tutorial-update', {
        detail: { 
          tutorial: payload.new,
          oldTutorial: payload.old
        }
      }))
    },
    onError: (error) => {
      console.error('教程实时同步错误:', error)
    }
  })
}

/**
 * 实时同步管理器
 */
export const realtimeManager = {
  /**
   * 检查Realtime是否可用
   */
  isRealtimeAvailable: async (): Promise<boolean> => {
    try {
      // 简单的连接测试
      const testChannel = supabaseAdmin.channel('connectivity_test')
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          supabaseAdmin.removeChannel(testChannel)
          resolve(false)
        }, 5000) // 5秒超时
        
        testChannel
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              clearTimeout(timeout)
              supabaseAdmin.removeChannel(testChannel)
              resolve(true)
            }
          })
      })
      
    } catch (error) {
      console.error('Realtime可用性检查失败:', error)
      return false
    }
  },

  /**
   * 获取实时连接状态
   */
  getConnectionStatus: () => {
    // 检查Supabase客户端连接状态
    return {
      connected: supabaseAdmin.realtime.isConnected(),
      channels: supabaseAdmin.realtime.channels.length,
      endpoint: supabaseAdmin.realtime.endPoint
    }
  },

  /**
   * 全局实时事件监听器
   */
  onRealtimeEvent: (eventType: string, callback: (detail: any) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail)
    window.addEventListener(`realtime-${eventType}`, handler as EventListener)
    
    return () => {
      window.removeEventListener(`realtime-${eventType}`, handler as EventListener)
    }
  }
}

/**
 * 实时同步配置组件 - 用于管理后台
 */
export const RealtimeConfig = {
  /**
   * 启用/禁用实时同步
   */
  toggleRealtime: (enabled: boolean) => {
    localStorage.setItem('realtime_enabled', enabled.toString())
    
    // 触发配置变更事件
    window.dispatchEvent(new CustomEvent('realtime-config-changed', {
      detail: { enabled }
    }))
  },

  /**
   * 获取实时同步配置
   */
  getRealtimeConfig: (): boolean => {
    try {
      return localStorage.getItem('realtime_enabled') === 'true'
    } catch {
      return false // 默认禁用
    }
  },

  /**
   * 监听配置变更
   */
  onConfigChange: (callback: (enabled: boolean) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail.enabled)
    window.addEventListener('realtime-config-changed', handler as EventListener)
    
    return () => {
      window.removeEventListener('realtime-config-changed', handler as EventListener)
    }
  }
}