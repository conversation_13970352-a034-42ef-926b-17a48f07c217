-- ==========================================
-- 创建管理员默认密码哈希
-- 初始密码：admin123 (生产环境需要立即修改)
-- ==========================================

-- 删除现有的管理员密码配置（如果存在）
DELETE FROM system_config WHERE config_key = 'admin_password';

-- 插入默认管理员密码哈希
-- 密码: admin123 (bcrypt hash with 12 rounds)
INSERT INTO system_config (config_key, config_value, description) VALUES (
    'admin_password',
    '$2a$12$8K2xXGF.Qw8YCRYvxqK8oOuI3.zVwZw8yTdQW7vKRVR2mKZKNwZmG',
    '管理员登录密码哈希值 - 请在生产环境中立即修改'
);

-- 验证插入是否成功
SELECT 
    config_key, 
    LEFT(config_value, 20) || '...' as password_hash_preview,
    description,
    created_at
FROM system_config 
WHERE config_key = 'admin_password';

-- 安全提醒信息
SELECT '⚠️ 安全提醒：默认密码为 admin123，请立即修改！' as security_warning;