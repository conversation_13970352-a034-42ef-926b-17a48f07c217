import { NextRequest, NextResponse } from "next/server"

const WECHAT_APPID = process.env.WECHAT_APPID || ""
const WECHAT_APPSECRET = process.env.WECHAT_APPSECRET || ""

export async function POST(request: NextRequest) {
  try {
    const { code, state } = await request.json()

    if (!code) {
      return NextResponse.json({ 
        success: false, 
        error: "缺少授权码" 
      }, { status: 400 })
    }

    // 开发环境下，如果没有配置真实的AppID和AppSecret，返回模拟数据
    if (!WECHAT_APPID || !WECHAT_APPSECRET || WECHAT_APPID === "your_wechat_appid") {
      console.log("开发模式：使用模拟微信登录数据")
      
      // 模拟延迟，让用户感觉到真实API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockUserInfo = {
        openid: `mock_openid_${Date.now()}`,
        unionid: `mock_unionid_${Date.now()}`,
        nickname: "微信用户" + Math.floor(Math.random() * 1000),
        headimgurl: "/placeholder.svg?height=40&width=40",
        sex: 1,
        province: "广东",
        city: "深圳",
        country: "中国"
      }

      return NextResponse.json({
        success: true,
        userInfo: mockUserInfo,
        isDemo: true
      })
    }

    // 生产环境：真实微信API调用
    try {
      // 1. 通过code换取access_token
      const tokenUrl = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${WECHAT_APPID}&secret=${WECHAT_APPSECRET}&code=${code}&grant_type=authorization_code`
      
      const tokenResponse = await fetch(tokenUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const tokenData = await tokenResponse.json()

      if (tokenData.errcode) {
        console.error("微信Token错误:", tokenData)
        return NextResponse.json({ 
          success: false, 
          error: `微信授权失败: ${tokenData.errmsg}` 
        }, { status: 400 })
      }

      const { access_token, openid, unionid } = tokenData

      // 2. 获取用户信息
      const userInfoUrl = `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}&lang=zh_CN`
      
      const userResponse = await fetch(userInfoUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const userInfo = await userResponse.json()

      if (userInfo.errcode) {
        console.error("微信用户信息错误:", userInfo)
        return NextResponse.json({ 
          success: false, 
          error: `获取用户信息失败: ${userInfo.errmsg}` 
        }, { status: 400 })
      }

      // 3. 可以在这里将用户信息保存到数据库
      // TODO: 保存用户信息到数据库，建立用户关联等

      return NextResponse.json({
        success: true,
        userInfo: {
          openid: userInfo.openid,
          unionid: userInfo.unionid,
          nickname: userInfo.nickname,
          headimgurl: userInfo.headimgurl,
          sex: userInfo.sex,
          province: userInfo.province,
          city: userInfo.city,
          country: userInfo.country
        },
        isDemo: false
      })

    } catch (apiError) {
      console.error("微信API调用错误:", apiError)
      return NextResponse.json({ 
        success: false, 
        error: "微信服务暂时不可用，请稍后重试" 
      }, { status: 500 })
    }

  } catch (error) {
    console.error("微信登录处理错误:", error)
    return NextResponse.json({ 
      success: false, 
      error: "服务器内部错误" 
    }, { status: 500 })
  }
}