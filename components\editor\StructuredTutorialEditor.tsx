'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import { useState, useCallback, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { 
  Bold, 
  Italic, 
  Strikethrough, 
  Code, 
  List, 
  ListOrdered, 
  Quote, 
  Undo, 
  Redo,
  Image as ImageIcon,
  Link as LinkIcon,
  Code2,
  BookOpen,
  Clock,
  Target,
  CheckCircle
} from 'lucide-react'

// ==========================================
// 结构化教程编辑器组件
// 支持添加章节属性，生成进度跟踪友好的HTML
// ==========================================

interface StructuredTutorialEditorProps {
  initialContent?: string
  onSave?: (content: string) => void
  onAutoSave?: (content: string) => void
  className?: string
  placeholder?: string
  editable?: boolean
}

interface ChapterData {
  id: string
  title: string
  estimatedTime: number
  type: 'intro' | 'chapter' | 'checkpoint' | 'interactive' | 'conclusion'
}

export function StructuredTutorialEditor({
  initialContent = '',
  onSave,
  onAutoSave,
  className = '',
  placeholder = '开始编写你的结构化教程内容...',
  editable = true
}: StructuredTutorialEditorProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [showChapterDialog, setShowChapterDialog] = useState(false)
  const [currentChapter, setCurrentChapter] = useState<ChapterData>({
    id: '',
    title: '',
    estimatedTime: 5,
    type: 'chapter'
  })
  
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
    ],
    content: initialContent,
    editable,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML()
      if (onAutoSave) {
        const autoSaveTimer = setTimeout(() => {
          onAutoSave(content)
        }, 2000)
        
        return () => clearTimeout(autoSaveTimer)
      }
    },
  })

  // 手动保存
  const handleSave = useCallback(async () => {
    if (!editor || !onSave) return
    
    setIsSaving(true)
    try {
      const content = editor.getHTML()
      await onSave(content)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }, [editor, onSave])

  // 插入章节
  const insertChapter = useCallback(() => {
    if (!editor) return
    
    const { id, title, estimatedTime, type } = currentChapter
    
    if (!id || !title) {
      alert('请填写章节ID和标题')
      return
    }
    
    // 生成章节HTML
    const chapterHtml = `
      <section data-section="${id}" 
               data-section-title="${title}" 
               data-estimated-time="${estimatedTime}">
        <h2>${title}</h2>
        <p>在这里编写章节内容...</p>
      </section>
    `
    
    editor.chain().focus().insertContent(chapterHtml).run()
    
    // 重置表单
    setCurrentChapter({
      id: '',
      title: '',
      estimatedTime: 5,
      type: 'chapter'
    })
    setShowChapterDialog(false)
  }, [editor, currentChapter])
  
  // 插入检查点
  const insertCheckpoint = useCallback(() => {
    if (!editor) return
    
    const checkpointHtml = `
      <div data-checkpoint="knowledge-check" 
           data-checkpoint-type="knowledge" 
           data-points="10"
           style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
        <h4>🎯 知识检查点</h4>
        <p>在这里添加检查点内容...</p>
      </div>
    `
    
    editor.chain().focus().insertContent(checkpointHtml).run()
  }, [editor])
  
  // 插入互动练习
  const insertInteractive = useCallback(() => {
    if (!editor) return
    
    const interactiveHtml = `
      <div data-interactive="exercise" 
           data-required="false"
           style="background: #dbeafe; border: 2px solid #3b82f6; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
        <h4>🎮 互动练习</h4>
        <p>在这里添加练习内容...</p>
      </div>
    `
    
    editor.chain().focus().insertContent(interactiveHtml).run()
  }, [editor])

  // 插入图片
  const insertImage = useCallback(async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('/api/admin/media', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success && editor) {
            editor.chain().focus().setImage({ src: result.data.url }).run()
          }
        } else {
          const url = window.prompt('上传失败，请输入图片URL:')
          if (url && editor) {
            editor.chain().focus().setImage({ src: url }).run()
          }
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        const url = window.prompt('上传失败，请输入图片URL:')
        if (url && editor) {
          editor.chain().focus().setImage({ src: url }).run()
        }
      }
    }
    input.click()
  }, [editor])

  // 插入链接
  const insertLink = useCallback(() => {
    const url = window.prompt('请输入链接URL:')
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }, [editor])

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault()
            handleSave()
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleSave])

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-gray-500">编辑器加载中...</div>
      </div>
    )
  }

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center gap-1 p-2 bg-gray-50 border-b border-gray-200 flex-wrap">
        {/* 结构化内容插入 */}
        <div className="flex items-center gap-1 mr-2">
          <Badge variant="outline" className="text-xs">结构化内容</Badge>
          
          <Dialog open={showChapterDialog} onOpenChange={setShowChapterDialog}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm" title="插入章节">
                <BookOpen className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>插入新章节</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="chapter-id">章节ID</Label>
                  <Input
                    id="chapter-id"
                    value={currentChapter.id}
                    onChange={(e) => setCurrentChapter(prev => ({...prev, id: e.target.value}))}
                    placeholder="例如: intro, 1, 2, conclusion"
                  />
                </div>
                <div>
                  <Label htmlFor="chapter-title">章节标题</Label>
                  <Input
                    id="chapter-title"
                    value={currentChapter.title}
                    onChange={(e) => setCurrentChapter(prev => ({...prev, title: e.target.value}))}
                    placeholder="例如: 课程介绍"
                  />
                </div>
                <div>
                  <Label htmlFor="estimated-time">预计时间(分钟)</Label>
                  <Input
                    id="estimated-time"
                    type="number"
                    value={currentChapter.estimatedTime}
                    onChange={(e) => setCurrentChapter(prev => ({...prev, estimatedTime: parseInt(e.target.value) || 5}))}
                  />
                </div>
                <div>
                  <Label htmlFor="chapter-type">章节类型</Label>
                  <Select value={currentChapter.type} onValueChange={(value: any) => setCurrentChapter(prev => ({...prev, type: value}))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="intro">介绍</SelectItem>
                      <SelectItem value="chapter">章节</SelectItem>
                      <SelectItem value="checkpoint">检查点</SelectItem>
                      <SelectItem value="interactive">互动</SelectItem>
                      <SelectItem value="conclusion">总结</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowChapterDialog(false)}>
                    取消
                  </Button>
                  <Button onClick={insertChapter}>
                    插入章节
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button variant="ghost" size="sm" onClick={insertCheckpoint} title="插入检查点">
            <Target className="h-4 w-4" />
          </Button>
          
          <Button variant="ghost" size="sm" onClick={insertInteractive} title="插入互动练习">
            <CheckCircle className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 基础格式化 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-gray-200' : ''}
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-gray-200' : ''}
        >
          <Italic className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'bg-gray-200' : ''}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={editor.isActive('code') ? 'bg-gray-200' : ''}
        >
          <Code className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 列表 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
        >
          <List className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 引用和代码块 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
        >
          <Quote className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'bg-gray-200' : ''}
        >
          <Code2 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 媒体插入 */}
        <Button variant="ghost" size="sm" onClick={insertImage}>
          <ImageIcon className="h-4 w-4" />
        </Button>
        
        <Button variant="ghost" size="sm" onClick={insertLink}>
          <LinkIcon className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 撤销重做 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>

        {/* 保存按钮 */}
        {onSave && (
          <>
            <Separator orientation="vertical" className="h-6 mx-1" />
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? '保存中...' : '保存 (Ctrl+S)'}
            </Button>
          </>
        )}
      </div>

      {/* 编辑器内容区域 */}
      <div className="prose prose-sm max-w-none">
        <EditorContent 
          editor={editor} 
          className="min-h-[500px] p-4 focus:outline-none"
          placeholder={placeholder}
        />
      </div>

      {/* 底部状态栏 */}
      <div className="flex justify-between items-center p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
        <div>
          字符数: {editor.storage.characterCount?.characters() || 0} | 
          单词数: {editor.storage.characterCount?.words() || 0}
        </div>
        <div>
          支持结构化进度跟踪 | Ctrl+S保存
        </div>
      </div>
    </div>
  )
}