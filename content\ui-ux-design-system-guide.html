<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>UI/UX 设计系统构建指南</title>
    <style>
        .tutorial-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-left: 4px solid #3b82f6;
            background: #f8fafc;
        }
        .checkpoint {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .interactive {
            background: #dbeafe;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .learning-objective {
            background: #d1fae5;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .progress-marker {
            background: #f3e8ff;
            border-left: 4px solid #8b5cf6;
            padding: 1rem;
            margin: 1rem 0;
        }
        .code-example {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            overflow-x: auto;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .tip {
            background: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 1rem;
            margin: 1rem 0;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .chapter-nav {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            padding: 1rem;
            background: #f1f5f9;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="tutorial-content">
        
        <!-- 教程介绍 -->
        <header data-section="intro" data-section-title="课程介绍" data-estimated-time="5">
            <h1>🎨 UI/UX 设计系统构建指南</h1>
            
            <div class="learning-objective" data-learning-objective="primary">
                <h3>🎯 课程学习目标</h3>
                <ul>
                    <li>理解设计系统的基本概念和重要性</li>
                    <li>掌握设计令牌(Design Tokens)的创建和使用</li>
                    <li>学会构建可复用的组件库</li>
                    <li>了解设计系统的维护和扩展策略</li>
                    <li>掌握团队协作和设计交付流程</li>
                </ul>
            </div>

            <div class="progress-marker">
                <p><strong>📊 课程结构：</strong> 本课程包含6个主要章节，预计学习时间120分钟</p>
                <p><strong>🛠️ 使用工具：</strong> Figma, React, Storybook, Styled Components</p>
                <p><strong>📈 难度级别：</strong> 中级（需要基础的设计和前端开发知识）</p>
            </div>
        </header>

        <!-- 第一章：设计系统基础 -->
        <section class="section" data-section="1" data-section-title="设计系统基础" data-estimated-time="20">
            <h2>第一章：设计系统基础</h2>
            
            <h3>1.1 什么是设计系统？</h3>
            <p>设计系统是一套<span class="highlight">完整的设计标准、组件库和指导原则</span>，用于在整个产品生态系统中创建一致的用户体验。它不仅仅是一个组件库，而是一个活的、可演化的系统。</p>

            <div class="tip">
                <strong>💡 专家提示：</strong> 设计系统的核心价值在于提高设计和开发效率，确保用户体验的一致性。
            </div>

            <h3>1.2 设计系统的核心组成</h3>
            <ul>
                <li><strong>设计令牌(Design Tokens)</strong> - 设计决策的最小单位</li>
                <li><strong>组件库</strong> - 可复用的UI组件</li>
                <li><strong>设计原则</strong> - 指导设计决策的准则</li>
                <li><strong>使用指南</strong> - 如何正确使用组件的文档</li>
                <li><strong>品牌规范</strong> - 视觉身份和语调指南</li>
            </ul>

            <div class="checkpoint" data-checkpoint="1-1" data-checkpoint-type="knowledge" data-points="15">
                <h4>🎯 检查点 1.1：基础概念理解</h4>
                <p>请确保你已经理解了设计系统的定义和核心组成部分。设计系统与组件库的主要区别是什么？</p>
                <details>
                    <summary>点击查看答案</summary>
                    <p>设计系统是一个更广泛的概念，包含设计原则、指南和流程，而组件库只是其中的一部分。</p>
                </details>
            </div>

            <h3>1.3 著名设计系统案例分析</h3>
            <div class="interactive" data-interactive="case-study" data-required="true">
                <h4>📝 案例分析练习</h4>
                <p>研究以下知名设计系统，分析它们的特点：</p>
                <ul>
                    <li><strong>Material Design (Google)</strong> - 基于纸质隐喻的设计语言</li>
                    <li><strong>Human Interface Guidelines (Apple)</strong> - 注重简洁和用户体验</li>
                    <li><strong>Ant Design</strong> - 企业级应用的设计语言</li>
                    <li><strong>Atlassian Design System</strong> - 协作工具的设计规范</li>
                </ul>
                <p><em>选择其中一个系统，分析其设计原则和组件特点（花费5-10分钟）</em></p>
            </div>
        </section>

        <!-- 第二章：设计令牌系统 -->
        <section class="section" data-section="2" data-section-title="设计令牌系统" data-estimated-time="25">
            <h2>第二章：设计令牌(Design Tokens)系统</h2>

            <h3>2.1 设计令牌的概念</h3>
            <p>设计令牌是<span class="highlight">设计决策的原子单位</span>，它们以代码友好的格式存储设计属性值。想象它们是设计系统的"基因"，决定了整个系统的视觉特征。</p>

            <h3>2.2 令牌的分类体系</h3>
            
            <h4>2.2.1 基础令牌(Primitive Tokens)</h4>
            <div class="code-example">
{
  "color": {
    "blue": {
      "50": "#eff6ff",
      "100": "#dbeafe",
      "500": "#3b82f6",
      "900": "#1e3a8a"
    }
  },
  "spacing": {
    "xs": "4px",
    "sm": "8px",
    "md": "16px",
    "lg": "24px"
  }
}
            </div>

            <h4>2.2.2 语义令牌(Semantic Tokens)</h4>
            <div class="code-example">
{
  "color": {
    "primary": "{color.blue.500}",
    "surface": "{color.gray.50}",
    "text": {
      "primary": "{color.gray.900}",
      "secondary": "{color.gray.600}"
    }
  }
}
            </div>

            <div class="checkpoint" data-checkpoint="2-1" data-checkpoint-type="practical" data-points="20">
                <h4>🎯 检查点 2.1：令牌系统设计</h4>
                <p>为一个电商网站设计基础的颜色令牌系统，包括：</p>
                <ul>
                    <li>主品牌色(3个层级)</li>
                    <li>辅助色系统</li>
                    <li>语义颜色(成功、警告、错误)</li>
                    <li>中性色阶(至少5个层级)</li>
                </ul>
            </div>

            <h3>2.3 令牌的命名规范</h3>
            <p>良好的命名规范是设计系统成功的关键。推荐使用<strong>BEM式命名</strong>或<strong>语义化命名</strong>：</p>

            <div class="tip">
                <strong>🎨 命名最佳实践：</strong>
                <ul>
                    <li>使用描述性而非值性的名称：<code>color-primary</code> 而非 <code>color-blue</code></li>
                    <li>保持层级清晰：<code>text.heading.large</code></li>
                    <li>考虑多主题支持：<code>surface.elevated</code></li>
                </ul>
            </div>

            <div class="interactive" data-interactive="naming-exercise" data-required="false">
                <h4>🎮 互动练习：令牌命名</h4>
                <p>为以下设计属性创建合适的令牌名称：</p>
                <ol>
                    <li>按钮的主要背景色</li>
                    <li>卡片的阴影效果</li>
                    <li>标题字体大小</li>
                    <li>页面间距</li>
                </ol>
            </div>
        </section>

        <!-- 第三章：色彩系统设计 -->
        <section class="section" data-section="3" data-section-title="色彩系统设计" data-estimated-time="20">
            <h2>第三章：色彩系统设计</h2>

            <h3>3.1 色彩理论基础</h3>
            <p>在设计系统中，色彩不仅仅是美学选择，更是<span class="highlight">传达信息和引导用户行为</span>的重要工具。</p>

            <h4>3.1.1 色彩的心理学作用</h4>
            <ul>
                <li><strong>蓝色</strong> - 信任、专业、稳定（适合金融、医疗行业）</li>
                <li><strong>绿色</strong> - 成功、自然、成长（适合环保、健康行业）</li>
                <li><strong>红色</strong> - 紧急、重要、行动（适合警告、CTA按钮）</li>
                <li><strong>橙色</strong> - 友好、活力、创造（适合社交、创意行业）</li>
            </ul>

            <h3>3.2 构建可访问的色彩系统</h3>
            <div class="warning">
                <strong>⚠️ 无障碍要求：</strong> 根据WCAG 2.1标准，正常文本需要4.5:1的对比度，大文本需要3:1的对比度。
            </div>

            <h4>3.2.1 对比度检查工具</h4>
            <ul>
                <li>WebAIM Contrast Checker</li>
                <li>Figma的Stark插件</li>
                <li>Chrome的Lighthouse工具</li>
            </ul>

            <div class="interactive" data-interactive="color-accessibility" data-required="true">
                <h4>🔍 可访问性测试</h4>
                <p>使用在线对比度检查工具，验证以下色彩组合的可访问性：</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #3b82f6; color: white; padding: 1rem; text-align: center;">
                        白色文字 on 蓝色背景
                    </div>
                    <div style="background: #fbbf24; color: white; padding: 1rem; text-align: center;">
                        白色文字 on 黄色背景
                    </div>
                    <div style="background: #f3f4f6; color: #6b7280; padding: 1rem; text-align: center;">
                        灰色文字 on 浅灰背景
                    </div>
                </div>
                <p>记录每组的对比度值，并判断是否符合WCAG标准。</p>
            </div>

            <div class="checkpoint" data-checkpoint="3-1" data-checkpoint-type="practical" data-points="25">
                <h4>🎯 检查点 3.1：色彩系统构建</h4>
                <p>设计一个完整的色彩系统，包含：</p>
                <ol>
                    <li>主色调(Primary) - 3个深浅层级</li>
                    <li>辅助色(Secondary) - 2个深浅层级</li>
                    <li>语义色彩 - 成功(绿)、警告(橙)、错误(红)、信息(蓝)</li>
                    <li>中性灰度 - 至少7个层级</li>
                    <li>确保所有文本色彩都通过WCAG AA标准</li>
                </ol>
            </div>
        </section>

        <!-- 第四章：排版系统 -->
        <section class="section" data-section="4" data-section-title="排版系统" data-estimated-time="15">
            <h2>第四章：排版系统(Typography)</h2>

            <h3>4.1 字体层级体系</h3>
            <p>良好的排版系统应该建立清晰的<span class="highlight">视觉层级</span>，帮助用户快速理解内容结构。</p>

            <h4>4.1.1 模块化字体尺寸</h4>
            <p>使用数学比例创建和谐的字体尺寸系统，常用比例包括：</p>
            <ul>
                <li><strong>大三度 (1.25)</strong> - 适合正文阅读</li>
                <li><strong>完全四度 (1.333)</strong> - 平衡的视觉节奏</li>
                <li><strong>完全五度 (1.5)</strong> - 更强的层级对比</li>
            </ul>

            <div class="code-example">
/* 基于1.25比例的字体系统 */
.text-xs   { font-size: 0.75rem; }  /* 12px */
.text-sm   { font-size: 0.875rem; } /* 14px */
.text-base { font-size: 1rem; }     /* 16px */
.text-lg   { font-size: 1.125rem; } /* 18px */
.text-xl   { font-size: 1.25rem; }  /* 20px */
.text-2xl  { font-size: 1.5rem; }   /* 24px */
.text-3xl  { font-size: 1.875rem; } /* 30px */
.text-4xl  { font-size: 2.25rem; }  /* 36px */
            </div>

            <h3>4.2 行高和字间距</h3>
            <div class="tip">
                <strong>📏 排版最佳实践：</strong>
                <ul>
                    <li>正文行高: 1.4-1.6倍</li>
                    <li>标题行高: 1.1-1.3倍</li>
                    <li>字间距: 通常保持默认，特殊情况下微调</li>
                </ul>
            </div>

            <div class="checkpoint" data-checkpoint="4-1" data-checkpoint-type="design" data-points="15">
                <h4>🎯 检查点 4.1：排版系统设计</h4>
                <p>创建一个排版系统，定义以下文本样式：</p>
                <ul>
                    <li>H1-H6 标题层级</li>
                    <li>正文、小字、caption样式</li>
                    <li>按钮、链接文本样式</li>
                    <li>确保在移动设备上的可读性</li>
                </ul>
            </div>
        </section>

        <!-- 第五章：组件库构建 -->
        <section class="section" data-section="5" data-section-title="组件库构建" data-estimated-time="30">
            <h2>第五章：组件库构建</h2>

            <h3>5.1 组件设计原则</h3>
            <p>设计系统中的组件应该遵循<strong>原子设计理论</strong>，从最小的原子组件构建复杂的界面。</p>

            <h4>5.1.1 原子设计层级</h4>
            <ol>
                <li><strong>原子(Atoms)</strong> - 按钮、输入框、标签等基础元素</li>
                <li><strong>分子(Molecules)</strong> - 搜索框(输入框+按钮)、表单字段等</li>
                <li><strong>有机体(Organisms)</strong> - 导航栏、页脚、产品列表等</li>
                <li><strong>模板(Templates)</strong> - 页面布局结构</li>
                <li><strong>页面(Pages)</strong> - 具体的页面实例</li>
            </ol>

            <h3>5.2 核心组件设计</h3>

            <h4>5.2.1 按钮组件系统</h4>
            <p>按钮是最重要的交互元素之一，需要考虑多种状态和变体：</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin: 1rem 0;">
                <button style="background: #3b82f6; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-weight: 500;">Primary</button>
                <button style="background: transparent; color: #3b82f6; border: 2px solid #3b82f6; padding: 10px 22px; border-radius: 6px; font-weight: 500;">Secondary</button>
                <button style="background: #ef4444; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-weight: 500;">Danger</button>
                <button style="background: #f3f4f6; color: #6b7280; border: none; padding: 12px 24px; border-radius: 6px; font-weight: 500;" disabled>Disabled</button>
            </div>

            <div class="code-example">
/* 按钮变体系统 */
.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-secondary {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}
            </div>

            <div class="interactive" data-interactive="component-design" data-required="true">
                <h4>🎨 组件设计练习</h4>
                <p>设计一个完整的输入框组件，包含以下状态：</p>
                <ul>
                    <li>默认状态</li>
                    <li>聚焦状态</li>
                    <li>错误状态</li>
                    <li>禁用状态</li>
                    <li>带有图标的变体</li>
                    <li>不同尺寸(小、中、大)</li>
                </ul>
                <p>考虑无障碍访问性，确保有清晰的标签和错误提示。</p>
            </div>

            <h3>5.3 状态管理</h3>
            <p>每个组件都需要考虑不同的交互状态：</p>
            <ul>
                <li><strong>Static</strong> - 组件的默认状态</li>
                <li><strong>Hover</strong> - 鼠标悬停时的视觉反馈</li>
                <li><strong>Active</strong> - 点击或选中时的状态</li>
                <li><strong>Focus</strong> - 键盘导航时的焦点状态</li>
                <li><strong>Disabled</strong> - 不可交互的禁用状态</li>
                <li><strong>Loading</strong> - 数据加载时的状态</li>
            </ul>

            <div class="checkpoint" data-checkpoint="5-1" data-checkpoint-type="practical" data-points="30">
                <h4>🎯 检查点 5.1：组件库构建</h4>
                <p>构建以下基础组件的完整设计规范：</p>
                <ol>
                    <li><strong>Button组件</strong> - 3种变体，5种状态，3种尺寸</li>
                    <li><strong>Input组件</strong> - 包含所有交互状态和错误处理</li>
                    <li><strong>Card组件</strong> - 至少2种布局变体</li>
                    <li><strong>Badge组件</strong> - 语义化颜色和尺寸系统</li>
                </ol>
                <p>每个组件都要包含使用指南和代码示例。</p>
            </div>
        </section>

        <!-- 第六章：工具和流程 -->
        <section class="section" data-section="6" data-section-title="工具和流程" data-estimated-time="20">
            <h2>第六章：工具和协作流程</h2>

            <h3>6.1 设计工具生态</h3>
            
            <h4>6.1.1 Figma 设计系统管理</h4>
            <ul>
                <li><strong>Components</strong> - 创建可复用的设计组件</li>
                <li><strong>Variants</strong> - 管理组件的不同状态和变体</li>
                <li><strong>Auto Layout</strong> - 响应式设计布局</li>
                <li><strong>Design Tokens</strong> - 使用插件管理设计令牌</li>
                <li><strong>Libraries</strong> - 跨项目共享组件库</li>
            </ul>

            <h4>6.1.2 开发工具链</h4>
            <div class="code-example">
// Storybook 组件文档
import { Button } from './Button';

export default {
  title: 'Design System/Button',
  component: Button,
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger']
    }
  }
};

export const Primary = {
  args: {
    variant: 'primary',
    children: 'Button'
  }
};
            </div>

            <h3>6.2 设计令牌同步流程</h3>
            <p>建立从设计到开发的<span class="highlight">自动化同步流程</span>：</p>
            <ol>
                <li><strong>设计阶段</strong> - 在Figma中定义设计令牌</li>
                <li><strong>导出阶段</strong> - 使用工具(如Figma Tokens)导出JSON</li>
                <li><strong>转换阶段</strong> - 使用Style Dictionary转换为各平台格式</li>
                <li><strong>分发阶段</strong> - 自动更新到各个代码仓库</li>
            </ol>

            <div class="tip">
                <strong>🔄 自动化建议：</strong> 使用GitHub Actions或类似的CI/CD工具，在设计令牌更新时自动触发构建和部署流程。
            </div>

            <h3>6.3 团队协作最佳实践</h3>
            
            <h4>6.3.1 设计师与开发者协作</h4>
            <ul>
                <li><strong>共同的语言</strong> - 使用一致的命名规范</li>
                <li><strong>定期同步</strong> - 每周review会议讨论系统更新</li>
                <li><strong>文档驱动</strong> - 每个组件都有详细的使用文档</li>
                <li><strong>版本管理</strong> - 语义化版本控制系统变更</li>
            </ul>

            <div class="interactive" data-interactive="workflow-design" data-required="false">
                <h4>🔄 工作流程设计</h4>
                <p>为你的团队设计一个设计系统维护流程，包括：</p>
                <ul>
                    <li>新组件的提案和审批流程</li>
                    <li>现有组件的修改流程</li>
                    <li>版本发布和变更通知机制</li>
                    <li>质量保证和测试流程</li>
                </ul>
            </div>

            <div class="checkpoint" data-checkpoint="6-1" data-checkpoint-type="planning" data-points="20">
                <h4>🎯 检查点 6.1：工具链搭建</h4>
                <p>规划一个完整的设计系统工具链：</p>
                <ol>
                    <li>选择合适的设计工具和插件</li>
                    <li>设计开发环境的技术栈</li>
                    <li>制定设计令牌的同步流程</li>
                    <li>建立组件文档和演示系统</li>
                    <li>设计团队协作的工作流程</li>
                </ol>
            </div>
        </section>

        <!-- 课程总结 -->
        <section class="section" data-section="conclusion" data-section-title="课程总结" data-estimated-time="10">
            <h2>🎉 课程总结</h2>

            <div class="learning-objective" data-learning-objective="summary">
                <h3>🏆 你已经掌握的技能</h3>
                <ul>
                    <li>✅ 理解设计系统的核心概念和价值</li>
                    <li>✅ 掌握设计令牌的创建和管理</li>
                    <li>✅ 能够构建可访问的色彩和排版系统</li>
                    <li>✅ 学会组件化设计思维</li>
                    <li>✅ 了解团队协作和工具链建设</li>
                </ul>
            </div>

            <h3>下一步行动计划</h3>
            <div class="progress-marker">
                <h4>🚀 进阶学习建议</h4>
                <ol>
                    <li><strong>实践项目</strong> - 为一个真实项目构建设计系统</li>
                    <li><strong>工具深化</strong> - 深入学习Figma变体和Auto Layout</li>
                    <li><strong>代码实现</strong> - 学习React/Vue组件库开发</li>
                    <li><strong>测试优化</strong> - 掌握设计系统的A/B测试方法</li>
                    <li><strong>社区参与</strong> - 贡献开源设计系统项目</li>
                </ol>
            </div>

            <h3>资源推荐</h3>
            <ul>
                <li><strong>书籍</strong>：《Atomic Design》by Brad Frost</li>
                <li><strong>网站</strong>：Design Systems Repo (designsystemsrepo.com)</li>
                <li><strong>工具</strong>：Figma, Storybook, Style Dictionary</li>
                <li><strong>社区</strong>：Design Systems Slack Community</li>
            </ul>

            <div class="checkpoint" data-checkpoint="final" data-checkpoint-type="completion" data-points="50">
                <h4>🎯 最终检查点：课程完成</h4>
                <p>恭喜你完成了UI/UX设计系统构建指南！</p>
                <p>完成这个检查点表示你已经：</p>
                <ul>
                    <li>学习了设计系统的所有核心概念</li>
                    <li>完成了所有实践练习</li>
                    <li>具备了构建设计系统的基础能力</li>
                </ul>
                <p><strong>课程完成时间：</strong> <span id="completion-time"></span></p>
            </div>
        </section>

        <!-- 导航 -->
        <div class="chapter-nav">
            <div>
                <strong>📚 总计：</strong> 6个章节，7个检查点，5个互动练习
            </div>
            <div>
                <strong>⏱️ 预计学习时间：</strong> 120分钟
            </div>
        </div>

    </div>

    <script>
        // 简单的进度跟踪脚本示例
        document.addEventListener('DOMContentLoaded', function() {
            // 记录页面加载时间
            const startTime = new Date();
            
            // 跟踪章节滚动
            const sections = document.querySelectorAll('[data-section]');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionId = entry.target.getAttribute('data-section');
                        const sectionTitle = entry.target.getAttribute('data-section-title');
                        console.log(`正在学习: ${sectionTitle} (${sectionId})`);
                        
                        // 这里可以发送进度数据到后端
                        // updateLearningProgress(sectionId, sectionTitle);
                    }
                });
            }, { threshold: 0.5 });
            
            sections.forEach(section => observer.observe(section));
            
            // 跟踪检查点完成
            const checkpoints = document.querySelectorAll('[data-checkpoint]');
            checkpoints.forEach(checkpoint => {
                checkpoint.addEventListener('click', function() {
                    const checkpointId = this.getAttribute('data-checkpoint');
                    console.log(`检查点被访问: ${checkpointId}`);
                    // 这里可以标记检查点为已访问
                });
            });
            
            // 计算完成时间
            window.addEventListener('beforeunload', function() {
                const endTime = new Date();
                const duration = Math.round((endTime - startTime) / 1000 / 60); // 分钟
                console.log(`学习时长: ${duration} 分钟`);
            });
        });
    </script>
</body>
</html>