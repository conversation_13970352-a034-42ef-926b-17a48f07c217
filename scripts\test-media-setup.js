#!/usr/bin/env node

/**
 * 通过Supabase客户端直接创建表（使用INSERT操作而不是DDL）
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function createTestMediaRecord() {
  try {
    console.log('🧪 测试媒体管理功能...\n');

    // 检查tutorial_media表是否存在，通过尝试查询来判断
    console.log('🔍 检查tutorial_media表...');
    const { error: checkError } = await supabaseAdmin
      .from('tutorial_media')
      .select('id')
      .limit(1);

    if (checkError) {
      console.log('❌ tutorial_media表不存在，需要在Supabase Dashboard中手动创建');
      console.log('\n📋 请在Supabase SQL编辑器中执行以下SQL:');
      console.log(`
-- 创建媒体资源表
CREATE TABLE IF NOT EXISTS tutorial_media (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  file_size BIGINT NOT NULL,
  width INTEGER,
  height INTEGER,
  duration INTEGER,
  storage_path TEXT NOT NULL,
  public_url TEXT NOT NULL,
  thumbnail_url TEXT,
  alt_text VARCHAR(255),
  caption TEXT,
  usage_count INTEGER DEFAULT 0,
  uploaded_by VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tutorial_media_tutorial_id ON tutorial_media(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_tutorial_media_type ON tutorial_media(file_type);
      `);
      return;
    }

    console.log('✅ tutorial_media表已存在');

    // 测试媒体记录查询
    const { data: mediaFiles, error: queryError } = await supabaseAdmin
      .from('tutorial_media')
      .select('*')
      .eq('tutorial_id', 1);

    if (queryError) {
      console.error('❌ 查询媒体文件失败:', queryError);
      return;
    }

    console.log(`📁 教程1的媒体文件: ${mediaFiles?.length || 0} 个`);

    if (mediaFiles && mediaFiles.length > 0) {
      mediaFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file.original_name} (${file.file_type}) - ${file.file_size} bytes`);
      });
    } else {
      console.log('📄 暂无媒体文件，可以测试上传功能');
    }

    // 测试创建一个虚拟媒体记录
    console.log('\n🧪 创建测试媒体记录...');
    const testMedia = {
      tutorial_id: 1,
      file_name: 'test-image.jpg',
      original_name: 'test-image.jpg',
      file_type: 'image',
      mime_type: 'image/jpeg',
      file_size: 1024000,
      width: 1920,
      height: 1080,
      storage_path: 'tutorials/1/test-image.jpg',
      public_url: 'https://example.com/test-image.jpg',
      alt_text: '测试图片',
      uploaded_by: 'system'
    };

    const { data: insertedMedia, error: insertError } = await supabaseAdmin
      .from('tutorial_media')
      .insert(testMedia)
      .select()
      .single();

    if (insertError) {
      console.error('❌ 创建测试媒体记录失败:', insertError);
    } else {
      console.log('✅ 测试媒体记录创建成功:', insertedMedia);
    }

    console.log('\n🎉 媒体管理功能测试完成！');

  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

createTestMediaRecord();