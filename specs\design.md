# 技术设计规格 - 知识商城

## 系统架构设计（重大更新）

### 整体架构
```
                    用户界面层 (Next.js + React)
                            |
                    智能学习跟踪层 (新增核心层)
                            |
                      API 网关层 (Next.js API Routes)
                            |
                 +----------+----------+
                 |                     |
           业务逻辑层                数据访问层
        (TypeScript 函数)      (Supabase Client)
                 |                     |
                 +----------+----------+
                            |
                    数据存储层 (PostgreSQL)
                            |
                    学习数据缓存层 (localStorage + 云端同步)
```

### 技术栈架构（完整更新）
- **前端框架**：Next.js 14 (App Router) - 最新稳定版
- **状态管理**：React Hooks + Context API + 学习进度Store
- **UI组件**：shadcn/ui (47个组件) + Radix UI + Tailwind CSS
- **富文本编辑**：TipTap + 结构化编辑器扩展
- **学习跟踪**：Intersection Observer + 智能内容解析
- **数据库**：PostgreSQL (Supabase 托管) + 17个数据表
- **认证**：Supabase Auth + 自定义权限系统
- **存储**：Supabase Storage + 媒体管理
- **类型系统**：TypeScript 5.x + Zod 运行时验证

## 数据库设计（完整实现）

### 核心数据表

#### 学习进度系统（新增核心模块）
```sql
-- 学习进度主表
CREATE TABLE learning_progress (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  tutorial_id INTEGER REFERENCES tutorials(id),
  progress_percentage INTEGER DEFAULT 0,
  time_spent INTEGER DEFAULT 0,
  current_section VARCHAR(100),
  completed_sections TEXT[],
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学习会话表
CREATE TABLE learning_sessions (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  tutorial_id INTEGER REFERENCES tutorials(id),
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  sections_visited TEXT[],
  total_time INTEGER DEFAULT 0,
  completed_in_session TEXT[],
  session_data JSONB
);

-- 用户成就表
CREATE TABLE user_achievements (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(255) NOT NULL,
  achievement_type VARCHAR(50) NOT NULL,
  achievement_data JSONB,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  tutorial_id INTEGER REFERENCES tutorials(id)
);
```

#### 内容管理系统（扩展）
```sql
-- 教程表（增强版）
CREATE TABLE tutorials (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT, -- 支持结构化HTML
  category_id INTEGER REFERENCES categories(id),
  tags TEXT[],
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft',
  estimated_time INTEGER DEFAULT 0, -- 预计学习时间
  difficulty_level VARCHAR(20) DEFAULT 'beginner',
  content_type VARCHAR(20) DEFAULT 'html', -- html/structured
  structure_data JSONB, -- 章节结构元数据
  view_count INTEGER DEFAULT 0,
  completion_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 媒体资源表
CREATE TABLE tutorial_media (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER REFERENCES tutorials(id),
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size INTEGER,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  uploaded_by VARCHAR(255)
);
```

#### 公告系统（新增完整模块）
```sql
-- 公告表
CREATE TABLE announcements (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  type VARCHAR(20) DEFAULT 'info',
  target_audience VARCHAR(20) DEFAULT 'all',
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 公告已读记录表
CREATE TABLE announcement_reads (
  id SERIAL PRIMARY KEY,
  announcement_id INTEGER REFERENCES announcements(id),
  user_identifier VARCHAR(255) NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(announcement_id, user_identifier)
);
```

### 关键索引设计（性能优化）
```sql
-- 学习进度优化索引
CREATE INDEX idx_learning_progress_user_tutorial ON learning_progress(user_identifier, tutorial_id);
CREATE INDEX idx_learning_progress_tutorial ON learning_progress(tutorial_id);
CREATE INDEX idx_learning_sessions_user ON learning_sessions(user_identifier);

-- 核心业务索引
CREATE INDEX idx_tutorials_category ON tutorials(category_id);
CREATE INDEX idx_tutorials_status ON tutorials(status);
CREATE INDEX idx_tutorial_keys_code ON tutorial_keys(key_code);
CREATE INDEX idx_user_unlocks_user ON user_unlocks(user_identifier);

-- 公告系统索引
CREATE INDEX idx_announcements_active ON announcements(is_active, created_at);
CREATE INDEX idx_announcement_reads_user ON announcement_reads(user_identifier);
```

## API 设计规范（大幅扩展）

### 学习进度API（新增核心模块）

#### 学习进度跟踪
```typescript
// POST /api/learning/progress
interface LearningProgressRequest {
  tutorialId: number
  status: 'started' | 'in_progress' | 'completed'
  progressPercentage: number
  timeSpent: number
  completedSections: string[]
  currentSection: string
  sessionData?: any
}

interface LearningProgressResponse {
  success: boolean
  data: {
    id: number
    progressPercentage: number
    totalTimeSpent: number
    achievements?: Achievement[]
  }
}
```

#### 学习会话管理
```typescript
// POST /api/learning/sessions
interface SessionRequest {
  tutorialId: number
  action: 'start' | 'update' | 'end'
  sectionId?: string
  timeSpent?: number
  sessionData?: any
}
```

### 增强的教程API
```typescript
// GET /api/tutorial/[id] - 返回增强数据
interface TutorialResponse {
  tutorial: Tutorial
  is_unlocked: boolean
  learning_progress?: LearningProgress
  structure_data?: ChapterSection[]
  estimated_sections?: number
  completion_stats?: {
    total_users: number
    completion_rate: number
    average_time: number
  }
}
```

### 公告系统API（新增）
```typescript
// GET /api/announcements
interface AnnouncementResponse {
  announcements: Announcement[]
  unread_count: number
}

// POST /api/announcements/[id]/read
interface MarkReadRequest {
  user_identifier: string
}
```

## 前端组件设计（重大升级）

### 学习组件架构

#### 核心学习组件
```typescript
// TableOfContents 组件
interface TableOfContentsProps {
  chapters: ChapterSection[]
  currentSection: string
  totalProgress: number
  totalTimeSpent: number
  onSectionClick: (sectionId: string) => void
  onMarkComplete: (sectionId: string) => void
}

// LearningProgress 组件
interface LearningProgressProps {
  tutorialId: number
  userId: string
  onProgressUpdate?: (progress: LearningProgress) => void
  displayMode?: 'compact' | 'detailed' | 'dashboard'
}
```

#### 智能内容解析
```typescript
// learning-utils.ts 核心功能
export interface ChapterSection {
  id: string
  title: string
  estimatedTime: number
  type: 'intro' | 'chapter' | 'checkpoint' | 'interactive' | 'conclusion'
  completed: boolean
  currentlyViewing: boolean
  subsections?: ChapterSection[]
  element?: Element
}

// 智能解析函数
export function parseContentSections(htmlContent: string): ChapterSection[]
export function calculateProgressPercentage(sections: ChapterSection[], completed: string[]): number
export function setupSectionObserver(sections: ChapterSection[], callback: Function): IntersectionObserver
export function estimateReadingTime(content: Element): number
```

### 双编辑器系统（创新设计）

#### 普通编辑器 (TutorialEditor.tsx)
- TipTap 富文本编辑器
- 自动HTML输出
- 基础格式化工具
- 媒体上传支持
- 自动保存机制

#### 结构化编辑器 (StructuredTutorialEditor.tsx)
- 继承普通编辑器所有功能
- 可视化章节属性添加
- 一键插入检查点和互动练习
- 实时结构预览
- 进度跟踪友好的HTML生成

```typescript
// 结构化内容示例
const structuredContent = `
<section data-section="intro" 
         data-section-title="课程介绍" 
         data-estimated-time="5">
  <h1>课程介绍</h1>
  <p>内容...</p>
</section>

<div data-checkpoint="knowledge-check" 
     data-checkpoint-type="knowledge" 
     data-points="10">
  <h4>🎯 知识检查点</h4>
  <p>检查点内容...</p>
</div>
`
```

## 安全设计（增强版）

### 学习数据安全
```typescript
// 学习进度数据加密存储
class SecureLearningStore {
  private encryptData(data: LearningProgress): string {
    // 客户端数据加密
  }
  
  private decryptData(encryptedData: string): LearningProgress {
    // 客户端数据解密
  }
}

// 服务端验证
function validateLearningProgress(data: LearningProgressRequest): boolean {
  // 进度数据合理性验证
  // 防止客户端数据伪造
}
```

### 内容访问控制
```typescript
// 分层访问控制
interface AccessControl {
  tutorial_access: boolean      // 基础教程访问
  progress_tracking: boolean    // 进度跟踪功能
  advanced_features: boolean    // 高级功能
  admin_access: boolean        // 管理权限
}
```

## 性能优化设计（学习功能特化）

### 学习数据缓存策略
```typescript
// 多层缓存设计
interface CacheStrategy {
  level1: 'memory'        // 组件状态缓存（实时）
  level2: 'localStorage'  // 浏览器本地缓存（持久）
  level3: 'supabase'     // 云端数据库（同步）
  sync_interval: 30000   // 30秒同步间隔
}

// 智能缓存更新
class LearningCache {
  // 增量更新策略
  updateProgress(delta: Partial<LearningProgress>): void
  
  // 冲突解决
  resolveConflict(local: LearningProgress, remote: LearningProgress): LearningProgress
  
  // 离线支持
  enableOfflineMode(): void
}
```

### 章节加载优化
```typescript
// 虚拟滚动和懒加载
interface VirtualScrollConfig {
  viewport_height: number
  item_height: number
  buffer_size: number
  lazy_load_threshold: number
}

// 内容预加载策略
class ContentPreloader {
  preloadNextSections(currentIndex: number, count: number): Promise<void>
  prefetchMedia(sectionId: string): Promise<void>
}
```

## 学习分析系统设计（新增核心功能）

### 学习行为数据模型
```typescript
interface LearningAnalytics {
  user_identifier: string
  tutorial_id: number
  learning_patterns: {
    peak_learning_hours: number[]     // 学习高峰时段
    session_duration_avg: number     // 平均会话时长
    completion_speed: number         // 完成速度
    revisit_frequency: number        // 重访频率
  }
  performance_metrics: {
    retention_rate: number           // 知识保持率
    completion_rate: number          // 完成率
    time_efficiency: number          // 学习效率
  }
}
```

### 个性化推荐算法
```typescript
class PersonalizationEngine {
  // 基于学习历史的推荐
  recommendByHistory(userId: string): Tutorial[]
  
  // 基于相似用户的推荐
  recommendBySimilarity(userId: string): Tutorial[]
  
  // 基于学习进度的推荐
  recommendByProgress(userId: string): Tutorial[]
}
```

## 移动端适配设计

### 响应式学习界面
```css
/* 移动端优化 */
@media (max-width: 768px) {
  .learning-layout {
    flex-direction: column;
  }
  
  .table-of-contents {
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .table-of-contents.active {
    transform: translateX(0);
  }
}
```

### 触摸优化
```typescript
// 移动端手势支持
interface TouchGestures {
  swipe_left: 'next_section'
  swipe_right: 'prev_section'
  double_tap: 'mark_complete'
  long_press: 'bookmark'
}
```

## 扩展性设计（面向未来）

### 插件化学习功能
```typescript
interface LearningPlugin {
  name: string
  version: string
  init: (context: LearningContext) => void
  hooks: {
    onSectionEnter?: (section: ChapterSection) => void
    onSectionComplete?: (section: ChapterSection) => void
    onProgressUpdate?: (progress: LearningProgress) => void
  }
}

// 插件注册系统
class LearningPluginManager {
  register(plugin: LearningPlugin): void
  execute(hook: string, data: any): void
}
```

### AI 集成预留接口
```typescript
// AI 学习助手接口
interface AILearningAssistant {
  analyzeProgress(progress: LearningProgress): Promise<LearningInsight>
  generateQuestions(content: string): Promise<Question[]>
  provideFeedback(answers: Answer[]): Promise<Feedback>
  recommendPath(userProfile: UserProfile): Promise<LearningPath>
}
```

### 多平台数据同步
```typescript
// 跨平台同步接口
interface CrossPlatformSync {
  platforms: ('web' | 'mobile' | 'desktop')[]
  sync_strategy: 'real_time' | 'periodic' | 'manual'
  conflict_resolution: 'server_wins' | 'client_wins' | 'merge'
}
```

## 监控和分析设计

### 学习效果监控
```typescript
interface LearningMetrics {
  // 用户维度
  user_engagement: number
  session_quality: number
  completion_prediction: number
  
  // 内容维度  
  content_effectiveness: number
  difficulty_assessment: number
  improvement_suggestions: string[]
  
  // 系统维度
  performance_metrics: {
    load_time: number
    interaction_latency: number
    sync_success_rate: number
  }
}
```

### 实时分析仪表板
```typescript
interface AnalyticsDashboard {
  real_time_learners: number
  completion_rates: Record<string, number>
  popular_sections: ChapterSection[]
  performance_bottlenecks: string[]
  user_feedback: Feedback[]
}
```

这份设计文档反映了系统的最新架构，特别强调了学习进度跟踪系统的创新设计和完整的技术实现方案。