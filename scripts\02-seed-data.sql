-- 插入初始数据

-- 插入默认分类
INSERT INTO categories (name, description) VALUES
('编程开发', '编程相关的教程内容'),
('设计创意', '设计和创意相关的教程'),
('商业营销', '商业和营销策略教程'),
('生活技能', '日常生活技能教程')
ON CONFLICT DO NOTHING;

-- 确保管理员密码正确插入
DELETE FROM system_config WHERE config_key = 'admin_password';

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('key_expiry_days', '30', '密钥默认过期天数'),
('max_key_attempts', '5', '密钥验证最大尝试次数'),
('site_title', '教程密钥系统', '网站标题'),
('admin_password', 'admin123', '管理员密码（实际使用时应该加密）');

-- 插入示例教程
INSERT INTO tutorials (title, description, content, category_id, tags, status, price) VALUES
('Next.js 全栈开发实战教程', '从零开始学习 Next.js 全栈开发，包含前端、后端、数据库等完整技术栈。适合有一定 React 基础的开发者学习。', 
'<h1>Next.js 全栈开发实战教程</h1>
<h2>课程介绍</h2>
<p>本教程将带你从零开始学习 Next.js 全栈开发，涵盖前端、后端、数据库等完整技术栈。</p>
<h2>学习目标</h2>
<ul>
<li>掌握 Next.js 13+ 的核心特性</li>
<li>学会使用 App Router 构建现代应用</li>
<li>理解服务端渲染和客户端渲染</li>
<li>掌握 API Routes 开发</li>
<li>学会集成数据库和认证系统</li>
</ul>
<h2>适用人群</h2>
<p>本教程适合有一定 React 基础的开发者，希望学习全栈开发的同学。</p>', 
1, ARRAY['Next.js', 'React', '全栈开发', 'TypeScript'], 'published', 199.00),

('UI/UX 设计系统构建指南', '学习如何构建完整的设计系统，包含组件库、设计规范、品牌指导等。从设计到开发的完整流程。', 
'<h1>UI/UX 设计系统构建指南</h1>
<h2>什么是设计系统</h2>
<p>设计系统是一套完整的设计标准、组件库和工具，用于创建一致的用户体验。</p>
<h2>课程内容</h2>
<ul>
<li>设计系统的基础概念</li>
<li>色彩系统和字体规范</li>
<li>组件库的设计和开发</li>
<li>设计令牌的使用</li>
<li>团队协作和维护</li>
</ul>
<h2>工具介绍</h2>
<p>我们将使用 Figma 进行设计，使用 React 和 Storybook 构建组件库。</p>
<h2>项目实战</h2>
<p>通过实际项目，学习如何从零构建一个完整的设计系统。</p>', 
2, ARRAY['UI设计', 'UX设计', '设计系统', 'Figma'], 'published', 299.00)
ON CONFLICT DO NOTHING;
