-- 测试HTML内容渲染的教程数据
-- 这个脚本会插入一个包含HTML内容的测试教程

INSERT INTO tutorials (
    title,
    description,
    content,
    category_id,
    tags,
    price,
    status
) VALUES (
    'HTML渲染测试教程',
    '测试HTML代码在教程中的渲染效果，包含各种HTML元素和样式。',
    '<section data-section="intro" data-section-title="HTML渲染测试" data-estimated-time="5">
        <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">🎨 HTML渲染功能测试</h2>
        <p>这个教程专门用来测试HTML代码在知识商城中的渲染效果。</p>
        
        <div style="background: #dbeafe; border-left: 4px solid #3b82f6; padding: 16px; margin: 20px 0; border-radius: 6px;">
            <strong>💡 测试说明：</strong>
            <p style="margin: 8px 0 0 0;">此内容包含各种HTML元素，用于验证渲染功能是否正常工作。</p>
        </div>
        
        <h3 style="color: #059669; margin-top: 24px;">✅ 支持的HTML功能</h3>
        <ul style="line-height: 1.8;">
            <li><strong>文本格式</strong>：<em>斜体</em>、<strong>粗体</strong>、<u>下划线</u>、<code style="background: #f3f4f6; padding: 2px 6px; border-radius: 4px;">代码</code></li>
            <li><strong>颜色样式</strong>：<span style="color: #dc2626;">红色文字</span>、<span style="color: #059669;">绿色文字</span>、<span style="background: #fef3c7; padding: 2px 6px;">高亮背景</span></li>
            <li><strong>布局元素</strong>：段落、列表、表格、分割线</li>
            <li><strong>交互元素</strong>：链接、按钮（仅样式，不含JS）</li>
        </ul>
        
        <hr style="border: none; border-top: 2px solid #e5e7eb; margin: 24px 0;">
        
        <h3 style="color: #7c3aed;">🎯 样式展示区域</h3>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
            <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 16px; border-radius: 8px;">
                <h4 style="color: #059669; margin: 0 0 8px 0;">✨ 成功样式</h4>
                <p style="margin: 0; color: #065f46;">这是一个绿色主题的信息框，适合显示成功信息。</p>
            </div>
            
            <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 16px; border-radius: 8px;">
                <h4 style="color: #dc2626; margin: 0 0 8px 0;">⚠️ 警告样式</h4>
                <p style="margin: 0; color: #991b1b;">这是一个红色主题的警告框，适合显示重要提醒。</p>
            </div>
        </div>
        
        <blockquote style="border-left: 4px solid #9333ea; background: #faf5ff; padding: 16px; margin: 20px 0; font-style: italic; color: #6b21a8;">
            "HTML内容可以包含丰富的样式和布局，让教程内容更加生动有趣。"
        </blockquote>
    </section>

    <section data-section="1" data-section-title="表格和列表测试" data-estimated-time="8">
        <h2 style="color: #2563eb;">📊 表格和列表展示</h2>
        
        <h3>📋 功能对比表</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 16px 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <thead>
                <tr style="background: #f8fafc;">
                    <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left; font-weight: 600; color: #374151;">功能特性</th>
                    <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: center; font-weight: 600; color: #374151;">支持状态</th>
                    <th style="border: 1px solid #e2e8f0; padding: 12px; text-align: left; font-weight: 600; color: #374151;">说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">HTML标签渲染</td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px; text-align: center;"><span style="color: #059669; font-weight: 600;">✅ 支持</span></td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">完全支持HTML标签和属性</td>
                </tr>
                <tr style="background: #f8fafc;">
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">内联样式</td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px; text-align: center;"><span style="color: #059669; font-weight: 600;">✅ 支持</span></td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">支持style属性定义样式</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">JavaScript交互</td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px; text-align: center;"><span style="color: #dc2626; font-weight: 600;">❌ 不支持</span></td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">出于安全考虑，不执行JS代码</td>
                </tr>
                <tr style="background: #f8fafc;">
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">响应式布局</td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px; text-align: center;"><span style="color: #059669; font-weight: 600;">✅ 支持</span></td>
                    <td style="border: 1px solid #e2e8f0; padding: 12px;">支持CSS Grid和Flexbox</td>
                </tr>
            </tbody>
        </table>
        
        <h3 style="margin-top: 32px;">🎯 学习重点列表</h3>
        <ol style="line-height: 1.8; counter-reset: item;">
            <li style="margin: 8px 0;"><strong>HTML结构</strong>：掌握基本的HTML标签和语义化结构</li>
            <li style="margin: 8px 0;"><strong>CSS样式</strong>：学会使用内联样式美化内容</li>
            <li style="margin: 8px 0;"><strong>响应式设计</strong>：了解如何让内容适配不同屏幕</li>
            <li style="margin: 8px 0;"><strong>可访问性</strong>：确保内容对所有用户友好</li>
        </ol>
        
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; border-radius: 12px; margin: 24px 0; text-align: center;">
            <h4 style="margin: 0 0 12px 0; color: white;">🚀 高级功能展示</h4>
            <p style="margin: 0; opacity: 0.9;">CSS渐变背景、圆角边框、阴影效果等现代CSS特性都可以正常使用！</p>
        </div>
    </section>

    <section data-section="2" data-section-title="代码展示测试" data-estimated-time="6">
        <h2 style="color: #2563eb;">💻 代码展示功能</h2>
        
        <p>以下展示了不同的代码展示方式：</p>
        
        <h3>📝 内联代码</h3>
        <p>在文本中可以使用 <code style="background: #f3f4f6; color: #374151; padding: 3px 6px; border-radius: 4px; font-family: ''Courier New'', monospace;">console.log()</code> 这样的内联代码。</p>
        
        <h3>📄 代码块展示</h3>
        <pre style="background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; overflow-x: auto; font-family: ''Courier New'', monospace; line-height: 1.5;"><code>// JavaScript 代码示例
function createTutorial(title, content) {
    return {
        id: generateId(),
        title: title,
        content: content,
        createdAt: new Date(),
        status: ''published''
    };
}

// 使用示例
const tutorial = createTutorial(
    ''HTML渲染测试'',
    ''&lt;h1&gt;Hello World&lt;/h1&gt;''
);

console.log(tutorial);</code></pre>
        
        <h3>🎨 CSS样式代码</h3>
        <pre style="background: #065f46; color: #d1fae5; padding: 20px; border-radius: 8px; overflow-x: auto; font-family: ''Courier New'', monospace; line-height: 1.5;"><code>/* CSS 样式示例 */
.tutorial-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

.highlight-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}</code></pre>
        
        <div style="background: #fffbeb; border: 1px solid #fed7aa; padding: 16px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #9a3412; margin: 0 0 8px 0;">💡 代码展示小贴士</h4>
            <ul style="margin: 0; color: #9a3412; line-height: 1.6;">
                <li>使用 <code style="background: #fed7aa; color: #9a3412; padding: 2px 4px; border-radius: 3px;">&lt;pre&gt;&lt;code&gt;</code> 标签展示代码块</li>
                <li>为不同语言使用不同的背景色区分</li>
                <li>确保代码具有良好的可读性和对比度</li>
            </ul>
        </div>
    </section>

    <section data-section="conclusion" data-section-title="测试总结" data-estimated-time="3">
        <h2 style="color: #2563eb;">🎉 HTML渲染测试总结</h2>
        
        <div style="background: #ecfdf5; border: 2px solid #10b981; padding: 24px; border-radius: 12px; margin: 20px 0;">
            <h3 style="color: #065f46; margin: 0 0 16px 0;">✅ 测试结果</h3>
            <p style="color: #065f46; margin: 0 0 12px 0;">如果你能看到上面的各种样式效果（彩色文字、表格、代码块、渐变背景等），说明HTML渲染功能工作正常！</p>
            
            <div style="display: flex; gap: 16px; flex-wrap: wrap; margin-top: 16px;">
                <span style="background: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-size: 14px;">✨ 样式渲染</span>
                <span style="background: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-size: 14px;">📊 表格支持</span>
                <span style="background: #8b5cf6; color: white; padding: 6px 12px; border-radius: 6px; font-size: 14px;">💻 代码高亮</span>
                <span style="background: #f59e0b; color: white; padding: 6px 12px; border-radius: 6px; font-size: 14px;">🎨 渐变效果</span>
            </div>
        </div>
        
        <h3>🚀 下一步计划</h3>
        <ul style="line-height: 1.8;">
            <li>继续完善编辑器功能，提供更多HTML模板</li>
            <li>添加更多样式类支持，提升视觉效果</li>
            <li>优化移动端显示效果</li>
            <li>集成更多交互元素（在安全范围内）</li>
        </ul>
        
        <div style="text-align: center; margin: 32px 0;">
            <div style="display: inline-block; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; padding: 16px 32px; border-radius: 50px; font-weight: 600; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                🎊 HTML渲染功能测试完成！
            </div>
        </div>
    </section>',
    1,
    ARRAY['HTML', '测试', '渲染', '样式', '前端'],
    0.00,
    'published'
);

-- 获取插入的教程ID（用于生成测试密钥）
DO $$
DECLARE
    tutorial_id INTEGER;
BEGIN
    -- 获取刚插入的教程ID
    SELECT id INTO tutorial_id FROM tutorials WHERE title = 'HTML渲染测试教程' ORDER BY created_at DESC LIMIT 1;
    
    -- 为这个教程生成一些测试密钥
    INSERT INTO tutorial_keys (tutorial_id, key_code, is_used) VALUES
    (tutorial_id, 'HTMLTEST001234567890ABCD', false),
    (tutorial_id, 'RENDER01234567890ABCDEFGH', false),
    (tutorial_id, 'STYLE1234567890ABCDEFGHI', false);
    
    RAISE NOTICE '已为教程ID % 创建HTML渲染测试教程和3个测试密钥', tutorial_id;
END $$;