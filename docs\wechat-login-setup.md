# 微信登录功能配置说明

## 功能概述

系统已实现完整的微信扫码登录功能，支持两种模式：

### 1. 演示模式（默认）
- 当未配置微信AppID时自动启用
- 提供模拟登录功能，用于开发和测试
- 登录界面会显示"演示模式"提示

### 2. 生产模式
- 配置真实微信AppID后自动启用
- 提供真实的微信扫码登录功能
- 支持弹窗和页面跳转两种登录方式

## 配置方法

### 第一步：申请微信开放平台
1. 访问 [微信开放平台](https://open.weixin.qq.com/)
2. 注册开发者账号
3. 创建网站应用
4. 提交审核（审核通过后才能使用）

### 第二步：配置环境变量
在 `.env.local` 文件中添加或修改以下配置：

```env
# 微信登录配置
WECHAT_APPID=你的微信AppID
WECHAT_APPSECRET=你的微信AppSecret

# 网站基础URL（生产环境需要修改为实际域名）
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

### 第三步：配置微信回调地址
在微信开放平台的应用设置中，配置授权回调域：
- 开发环境：`localhost:3000`（或实际端口）
- 生产环境：`yourdomain.com`

## API端点说明

### 1. 获取授权URL
- **端点**：`GET /api/wechat/auth`
- **功能**：生成微信授权链接
- **返回**：包含授权URL和二维码URL

### 2. 处理登录回调
- **端点**：`POST /api/wechat/login`
- **功能**：处理微信回调，获取用户信息
- **参数**：`{ code, state }`

### 3. 回调页面
- **路径**：`/wechat/callback`
- **功能**：处理微信授权回调，显示登录状态

## 安全特性

### 1. CSRF防护
- 使用随机生成的state参数
- 验证回调时的state一致性

### 2. 敏感信息保护
- AppSecret仅在服务端使用
- Access Token不暴露给客户端

### 3. 错误处理
- 完整的错误处理和用户提示
- 网络异常和API错误的降级处理

## 用户体验

### 1. 登录流程
1. 用户点击"微信登录"按钮
2. 弹出登录对话框
3. 演示模式：点击按钮模拟登录
4. 生产模式：弹窗或跳转到微信授权页面
5. 用户扫码确认后自动完成登录

### 2. 状态持久化
- 用户登录状态保存到localStorage
- 页面刷新后自动恢复登录状态
- 用户可以手动退出登录

### 3. 界面适配
- 响应式设计，支持PC和移动端
- 演示模式和生产模式的区别化显示
- 友好的错误提示和加载状态

## 技术实现

### 1. 后端API (Next.js App Router)
- `/app/api/wechat/auth/route.ts` - 生成授权URL
- `/app/api/wechat/login/route.ts` - 处理登录逻辑

### 2. 前端组件
- `/components/wechat-login-dialog.tsx` - 登录对话框
- `/app/wechat/callback/page.tsx` - 回调处理页面

### 3. 主要功能
- OAuth2.0 授权码模式
- 用户信息获取和本地存储
- 跨窗口消息通信
- 错误处理和状态管理

## 开发提示

### 1. 调试建议
- 查看浏览器控制台的网络请求
- 检查localStorage中的用户信息
- 验证环境变量是否正确加载

### 2. 常见问题
- **回调地址不匹配**：确保微信平台配置的域名与实际域名一致
- **AppSecret泄露**：仅在服务端使用，不要提交到版本控制
- **弹窗被阻止**：现代浏览器可能阻止弹窗，提供备用链接

### 3. 测试方法
- 演示模式：直接测试模拟登录流程
- 生产模式：需要真实的微信AppID和有效的回调域名

---

**注意**：使用真实微信登录功能需要微信开放平台的审核通过，审核期间可以使用演示模式进行开发和测试。