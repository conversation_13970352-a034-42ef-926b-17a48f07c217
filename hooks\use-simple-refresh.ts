/**
 * 简化版数据刷新Hook - 修复无限循环问题
 * 临时替代复杂的useSmartRefresh
 */

import { useState, useEffect, useCallback, useRef } from 'react'

interface SimpleRefreshOptions {
  url: string
  interval?: number
  enabled?: boolean
}

interface SimpleRefreshResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refresh: () => Promise<void>
}

/**
 * 简化版数据刷新Hook
 * 避免复杂依赖，专注核心功能
 */
export function useSimpleRefresh<T = any>(
  options: SimpleRefreshOptions
): SimpleRefreshResult<T> {
  
  const { url, interval = 30000, enabled = true } = options

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const abortControllerRef = useRef<AbortController>()
  const intervalRef = useRef<NodeJS.Timeout>()
  const mountedRef = useRef(true)

  // 基础数据获取函数
  const fetchData = useCallback(async (force = false) => {
    console.log('📞 fetchData 被调用:', {
      enabled,
      mounted: mountedRef.current,
      force,
      url
    })

    if (!enabled) {
      console.log('🚫 数据获取被禁用 (enabled=false)')
      return
    }

    if (!mountedRef.current) {
      console.log('🚫 组件未挂载 (mountedRef.current=false)')
      return
    }

    try {
      setLoading(true)
      setError(null)

      // 只有在强制刷新时才取消之前的请求
      if (force && abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 如果没有控制器或者被取消了，创建新的
      if (!abortControllerRef.current || abortControllerRef.current.signal.aborted) {
        abortControllerRef.current = new AbortController()
      }

      const requestUrl = force ? `${url}?_t=${Date.now()}` : url

      console.log('🚀 发起API请求:', requestUrl)

      const response = await fetch(requestUrl, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Cache-Control': force ? 'no-cache' : 'max-age=60'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      
      console.log('✅ API请求成功:', result)
      
      if (mountedRef.current) {
        setData(result)
      }

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🚫 请求被取消:', url)
        return
      }
      
      console.error('❌ API请求失败:', {
        url,
        error: err instanceof Error ? err.message : 'Unknown error',
        errorName: err instanceof Error ? err.name : 'Unknown'
      })
      
      if (mountedRef.current) {
        setError(err instanceof Error ? err : new Error('Fetch failed'))
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }, [url, enabled])

  // 手动刷新函数
  const refresh = useCallback(() => fetchData(true), [fetchData])

  // 初始化和轮询
  useEffect(() => {
    if (!enabled) {
      console.log('🚫 Hook被禁用，跳过初始化')
      return
    }

    console.log('🔄 初始化数据获取 Hook:', url)

    // 确保组件已挂载后再开始数据获取
    const timeoutId = setTimeout(() => {
      if (mountedRef.current) {
        console.log('⏰ 延迟执行：开始数据获取')
        fetchData()
      } else {
        console.log('⚠️ 组件未挂载，跳过数据获取')
      }
    }, 100)

    // 设置定期轮询
    if (interval > 0) {
      intervalRef.current = setInterval(() => {
        if (document.visibilityState === 'visible' && mountedRef.current) {
          fetchData()
        }
      }, interval)
    }

    return () => {
      clearTimeout(timeoutId)
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [enabled, interval, fetchData])

  // 组件挂载状态管理
  useEffect(() => {
    mountedRef.current = true
    console.log('🔗 组件已挂载，设置 mountedRef = true')
    
    return () => {
      console.log('🔌 组件卸载，设置 mountedRef = false')
      mountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return { data, loading, error, refresh }
}

/**
 * 教程数据简化版Hook
 */
export function useTutorialsSimple(enabled = true) {
  return useSimpleRefresh<{
    success: boolean
    data: any[]
    pagination?: any
  }>({
    url: '/api/public/tutorials',
    interval: 30000,
    enabled
  })
}