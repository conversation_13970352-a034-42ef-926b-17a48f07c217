/**
 * 缓存管理系统 - 智能失效和重新验证
 * 解决教程更新后首页显示延迟问题
 */

import { NextRequest, NextResponse } from "next/server"
import { revalidateTag, revalidatePath } from "next/cache"

// ==========================================
// 缓存标签定义 - 精细化管理
// ==========================================

export const CACHE_TAGS = {
  // 教程相关缓存
  TUTORIALS_ALL: 'tutorials:all',
  TUTORIALS_PUBLIC: 'tutorials:public', 
  TUTORIALS_ADMIN: 'tutorials:admin',
  TUTORIAL_DETAIL: (id: number) => `tutorial:${id}`,
  
  // 分类相关缓存
  CATEGORIES: 'categories:all',
  CATEGORY_TUTORIALS: (categoryId: number) => `category:${categoryId}:tutorials`,
  
  // 用户相关缓存
  USER_UNLOCKS: (userId: string) => `user:${userId}:unlocks`,
  
  // 搜索和筛选缓存
  TUTORIALS_SEARCH: 'tutorials:search',
  TUTORIALS_FILTER: 'tutorials:filter'
} as const

// ==========================================
// 缓存策略配置
// ==========================================

export const CACHE_STRATEGIES = {
  // 即时更新策略 - 管理员操作后立即生效
  IMMEDIATE: {
    revalidateIn: 0,
    tags: ['immediate'],
    headers: {
      'Cache-Control': 'no-cache, must-revalidate',
      'X-Cache-Strategy': 'immediate'
    }
  },
  
  // 短期缓存策略 - 频繁更新的内容
  SHORT_TERM: {
    revalidateIn: 60, // 1分钟
    tags: ['short-term'],
    headers: {
      'Cache-Control': 'public, max-age=60, stale-while-revalidate=120',
      'X-Cache-Strategy': 'short-term'
    }
  },
  
  // 中期缓存策略 - 一般内容
  MEDIUM_TERM: {
    revalidateIn: 300, // 5分钟
    tags: ['medium-term'],
    headers: {
      'Cache-Control': 'public, max-age=300, stale-while-revalidate=600',
      'X-Cache-Strategy': 'medium-term'
    }
  },
  
  // 长期缓存策略 - 静态内容
  LONG_TERM: {
    revalidateIn: 3600, // 1小时
    tags: ['long-term'],
    headers: {
      'Cache-Control': 'public, max-age=3600, stale-while-revalidate=7200',
      'X-Cache-Strategy': 'long-term'
    }
  }
} as const

// ==========================================
// 缓存管理器类
// ==========================================

export class CacheManager {
  
  /**
   * 教程相关缓存失效
   */
  static async invalidateTutorials(options: {
    type?: 'all' | 'public' | 'admin'
    tutorialId?: number
    categoryId?: number
  } = {}) {
    const { type = 'all', tutorialId, categoryId } = options
    
    try {
      console.log('🔄 开始失效教程缓存:', options)
      
      // 基础教程缓存失效
      const tagsToInvalidate = []
      
      if (type === 'all' || type === 'public') {
        tagsToInvalidate.push(CACHE_TAGS.TUTORIALS_PUBLIC)
        tagsToInvalidate.push(CACHE_TAGS.TUTORIALS_ALL)
        tagsToInvalidate.push(CACHE_TAGS.TUTORIALS_SEARCH)
        tagsToInvalidate.push(CACHE_TAGS.TUTORIALS_FILTER)
      }
      
      if (type === 'all' || type === 'admin') {
        tagsToInvalidate.push(CACHE_TAGS.TUTORIALS_ADMIN)
      }
      
      // 特定教程缓存失效
      if (tutorialId) {
        tagsToInvalidate.push(CACHE_TAGS.TUTORIAL_DETAIL(tutorialId))
      }
      
      // 分类相关缓存失效
      if (categoryId) {
        tagsToInvalidate.push(CACHE_TAGS.CATEGORY_TUTORIALS(categoryId))
      }
      
      // 执行缓存失效
      for (const tag of tagsToInvalidate) {
        revalidateTag(tag)
        console.log(`✅ 已失效缓存标签: ${tag}`)
      }
      
      // 路径级别重新验证
      revalidatePath('/')
      revalidatePath('/api/public/tutorials')
      revalidatePath('/admin')
      
      console.log('✅ 教程缓存失效完成')
      
    } catch (error) {
      console.error('❌ 缓存失效失败:', error)
      throw error
    }
  }
  
  /**
   * 用户解锁缓存失效
   */
  static async invalidateUserUnlocks(userId: string) {
    try {
      console.log('🔄 失效用户解锁缓存:', userId)
      
      revalidateTag(CACHE_TAGS.USER_UNLOCKS(userId))
      revalidatePath('/my-tutorials')
      revalidatePath('/api/user-unlocks')
      
      console.log('✅ 用户解锁缓存失效完成')
      
    } catch (error) {
      console.error('❌ 用户缓存失效失败:', error)
      throw error
    }
  }
  
  /**
   * 全站缓存失效 - 紧急使用
   */
  static async invalidateAll() {
    try {
      console.log('🔄 开始全站缓存失效')
      
      // 失效所有教程相关缓存
      await this.invalidateTutorials({ type: 'all' })
      
      // 失效分类缓存
      revalidateTag(CACHE_TAGS.CATEGORIES)
      
      // 路径级重新验证
      revalidatePath('/')
      revalidatePath('/admin')
      revalidatePath('/my-tutorials')
      
      console.log('✅ 全站缓存失效完成')
      
    } catch (error) {
      console.error('❌ 全站缓存失效失败:', error)
      throw error
    }
  }
  
  /**
   * 创建带缓存标签的响应
   */
  static createCachedResponse<T>(
    data: T, 
    strategy: keyof typeof CACHE_STRATEGIES,
    tags: string[] = [],
    additionalHeaders: Record<string, string> = {}
  ): NextResponse {
    const cacheStrategy = CACHE_STRATEGIES[strategy]
    
    const headers = {
      ...cacheStrategy.headers,
      ...additionalHeaders,
      'X-Cache-Tags': tags.join(','),
      'X-Timestamp': new Date().toISOString()
    }
    
    const response = NextResponse.json(data, { headers })
    
    // 添加缓存标签
    if (tags.length > 0) {
      // Next.js 13+ 标签缓存
      response.headers.set('Cache-Tags', tags.join(','))
    }
    
    return response
  }
  
  /**
   * 检查是否为管理员请求 - 决定缓存策略
   */
  static isAdminRequest(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    
    // 检查是否来自管理页面
    return referer.includes('/admin') || 
           request.url.includes('/admin') ||
           userAgent.includes('admin-client')
  }
  
  /**
   * 智能缓存策略选择
   */
  static selectCacheStrategy(
    request: NextRequest,
    dataType: 'tutorials' | 'categories' | 'user-data'
  ): keyof typeof CACHE_STRATEGIES {
    
    // 管理员请求使用短期缓存
    if (this.isAdminRequest(request)) {
      return 'SHORT_TERM'
    }
    
    // 用户数据使用短期缓存
    if (dataType === 'user-data') {
      return 'SHORT_TERM'
    }
    
    // 分类数据使用长期缓存
    if (dataType === 'categories') {
      return 'LONG_TERM'
    }
    
    // 教程数据使用中期缓存
    return 'MEDIUM_TERM'
  }
}

// ==========================================
// 缓存中间件辅助函数
// ==========================================

/**
 * API路由缓存装饰器
 */
export function withCache<T extends (...args: any[]) => Promise<NextResponse>>(
  handler: T,
  options: {
    strategy?: keyof typeof CACHE_STRATEGIES
    tags?: string[]
    invalidateOnMutation?: boolean
  } = {}
): T {
  return (async (...args: any[]) => {
    const [request] = args as [NextRequest]
    
    try {
      // 执行原始处理器
      const response = await handler(...args)
      
      // 如果是变更操作，触发相关缓存失效
      if (options.invalidateOnMutation && 
          ['POST', 'PUT', 'DELETE'].includes(request.method)) {
        
        // 异步执行缓存失效，不阻塞响应
        setImmediate(async () => {
          try {
            await CacheManager.invalidateTutorials()
          } catch (error) {
            console.error('异步缓存失效失败:', error)
          }
        })
      }
      
      return response
      
    } catch (error) {
      console.error('缓存装饰器错误:', error)
      throw error
    }
  }) as T
}

/**
 * 前端缓存刷新辅助函数
 */
export const clientCacheUtils = {
  /**
   * 强制刷新指定URL
   */
  forceRefresh: (url: string) => {
    const timestamp = Date.now()
    return `${url}${url.includes('?') ? '&' : '?'}_t=${timestamp}`
  },
  
  /**
   * 清除所有fetch缓存
   */
  clearFetchCache: () => {
    if (typeof window !== 'undefined' && 'caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name)
        })
      })
    }
  },
  
  /**
   * 智能刷新策略
   */
  smartRefresh: async (url: string, maxRetries = 3) => {
    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await fetch(clientCacheUtils.forceRefresh(url), {
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
        
        if (response.ok) {
          return response.json()
        }
        
        throw new Error(`HTTP ${response.status}`)
        
      } catch (error) {
        console.warn(`刷新尝试 ${i + 1} 失败:`, error)
        
        if (i === maxRetries - 1) {
          throw error
        }
        
        // 指数退避延迟
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
      }
    }
  }
}

// ==========================================
// 类型定义
// ==========================================

export interface CacheInvalidationOptions {
  immediate?: boolean
  background?: boolean
  retries?: number
  scope?: 'local' | 'global' | 'selective'
}

export interface CacheStats {
  totalInvalidations: number
  lastInvalidation: string
  cacheHitRate: number
  averageResponseTime: number
}