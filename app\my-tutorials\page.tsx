"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BookOpen,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Clock,
  CheckCircle,
  PlayCircle,
  Unlock,
  Calendar,
  Tag,
  Grid3X3,
  List,
  ArrowLeft,
  Star,
  TrendingUp,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_name: string
  tags: string[]
  price: number
  published_status: string // 发布状态: published, archived, draft
  created_at: string
  unlocked_at: string
  progress: number // 0-100
  learning_status: "not_started" | "in_progress" | "completed" // 学习状态
  last_accessed: string
  reading_time: number // in minutes
  difficulty: "beginner" | "intermediate" | "advanced"
  rating: number // 1-5
}

interface Category {
  id: number
  name: string
  count: number
}

type ViewMode = "grid" | "list"
type SortBy = "title" | "unlocked_at" | "last_accessed" | "progress" | "rating"
type SortOrder = "asc" | "desc"
type FilterStatus = "all" | "not_started" | "in_progress" | "completed"

export default function MyTutorialsPage() {
  const router = useRouter()
  const { toast } = useToast()

  // State management
  const [tutorials, setTutorials] = useState<Tutorial[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all")
  const [sortBy, setSortBy] = useState<SortBy>("unlocked_at")
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc")
  const [viewMode, setViewMode] = useState<ViewMode>("grid")

  useEffect(() => {
    loadUserTutorials()
  }, [])

  const loadUserTutorials = async () => {
    setLoading(true)
    try {
      // 获取用户解锁的教程列表
      const unlocksResponse = await fetch("/api/user-unlocks")
      if (unlocksResponse.ok) {
        const unlocksResult = await unlocksResponse.json()
        const unlocksData = unlocksResult.success ? unlocksResult.data : unlocksResult
        
        // 获取用户学习进度统计
        const progressResponse = await fetch("/api/learning/progress")
        let progressData = {}
        if (progressResponse.ok) {
          const progressResult = await progressResponse.json()
          if (progressResult.success && progressResult.data.userStats) {
            progressData = progressResult.data.userStats
          }
        }
        
        // 为每个教程获取详细的学习进度
        const tutorialsWithProgress = await Promise.all(
          unlocksData.map(async (tutorial: any) => {
            let tutorialProgress = {
              progress: 0,
              learning_status: "not_started" as const, // 改为learning_status
              last_accessed: tutorial.unlocked_at,
              reading_time: 30, // 默认预估时间
              difficulty: "intermediate" as const,
              rating: 4
            }
            
            try {
              // 获取特定教程的学习进度
              const tutorialProgressResponse = await fetch(`/api/learning/progress?tutorialId=${tutorial.id}`)
              if (tutorialProgressResponse.ok) {
                const tutorialProgressResult = await tutorialProgressResponse.json()
                if (tutorialProgressResult.success && tutorialProgressResult.data.tutorialProgress.length > 0) {
                  const latestProgress = tutorialProgressResult.data.tutorialProgress[0]
                  tutorialProgress = {
                    progress: Math.round(latestProgress.progress_percentage || 0),
                    learning_status: latestProgress.status || "not_started", // 改为learning_status
                    last_accessed: latestProgress.last_accessed_at || tutorial.unlocked_at,
                    reading_time: Math.round((latestProgress.total_time_spent || 0) / 60) || 30,
                    difficulty: "intermediate", // 可以后续从教程metadata获取
                    rating: 4 // 可以后续从用户评分获取
                  }
                }
              }
            } catch (error) {
              console.warn(`Failed to load progress for tutorial ${tutorial.id}:`, error)
            }
            
            return {
              ...tutorial,
              published_status: tutorial.status, // 保留原始的发布状态
              ...tutorialProgress,
              unlocked_at: tutorial.unlocked_at
            }
          })
        )
        
        setTutorials(tutorialsWithProgress)

        // Generate categories with counts
        const categoryMap = new Map()
        tutorialsWithProgress.forEach((tutorial: Tutorial) => {
          const count = categoryMap.get(tutorial.category_name) || 0
          categoryMap.set(tutorial.category_name, count + 1)
        })

        const categoriesWithCount = Array.from(categoryMap.entries()).map(([name, count], index) => ({
          id: index + 1,
          name,
          count,
        }))

        setCategories(categoriesWithCount)
      } else {
        console.warn("Failed to load user unlocks, using mock data")
        // 保留原有的Mock数据作为后备
        const mockTutorials: Tutorial[] = [
          {
            id: 1,
            title: "Next.js 全栈开发实战教程",
            description: "从零开始学习 Next.js 全栈开发，包含前端、后端、数据库等完整技术栈。",
            content: "",
            category_name: "编程开发",
            tags: ["Next.js", "React", "全栈开发", "TypeScript"],
            price: 199.0,
            created_at: "2024-01-15T00:00:00Z",
            unlocked_at: "2024-01-20T10:30:00Z",
            progress: 0,
            status: "not_started",
            last_accessed: "2024-01-20T10:30:00Z",
            reading_time: 45,
            difficulty: "intermediate",
            rating: 5,
          }
        ]
        setTutorials(mockTutorials)
        setCategories([{ id: 1, name: "编程开发", count: 1 }])
      }
    } catch (error) {
      console.error("Failed to load tutorials:", error)
      toast({
        title: "加载失败",
        description: "无法加载教程列表，请稍后重试",
        variant: "destructive",
      })
      // 使用空数据而不是错误状态
      setTutorials([])
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  // Filter and sort tutorials
  const filteredAndSortedTutorials = tutorials
    .filter((tutorial) => {
      const matchesSearch =
        tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tutorial.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tutorial.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesCategory = selectedCategory === "all" || tutorial.category_name === selectedCategory
      const matchesStatus = filterStatus === "all" || tutorial.learning_status === filterStatus

      return matchesSearch && matchesCategory && matchesStatus
    })
    .sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case "title":
          comparison = a.title.localeCompare(b.title)
          break
        case "unlocked_at":
          comparison = new Date(a.unlocked_at).getTime() - new Date(b.unlocked_at).getTime()
          break
        case "last_accessed":
          comparison = new Date(a.last_accessed).getTime() - new Date(b.last_accessed).getTime()
          break
        case "progress":
          comparison = a.progress - b.progress
          break
        case "rating":
          comparison = a.rating - b.rating
          break
        default:
          comparison = 0
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

  const getStatusBadge = (status: Tutorial["status"], progress: number) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            已完成
          </Badge>
        )
      case "in_progress":
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <PlayCircle className="h-3 w-3 mr-1" />
            进行中 {progress}%
          </Badge>
        )
      case "not_started":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            未开始
          </Badge>
        )
    }
  }

  const getDifficultyBadge = (difficulty: Tutorial["difficulty"]) => {
    const colors = {
      beginner: "bg-green-100 text-green-800",
      intermediate: "bg-yellow-100 text-yellow-800",
      advanced: "bg-red-100 text-red-800",
    }

    const labels = {
      beginner: "初级",
      intermediate: "中级",
      advanced: "高级",
    }

    return <Badge className={colors[difficulty]}>{labels[difficulty]}</Badge>
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star key={star} className={`h-4 w-4 ${star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
        ))}
      </div>
    )
  }

  const TutorialGridCard = ({ tutorial }: { tutorial: Tutorial }) => (
    <Card className="h-full flex flex-col transition-all duration-200 hover:shadow-lg border-l-4 border-l-blue-500">
      <CardHeader className="pb-2 flex-shrink-0 tutorial-card-header">
        <div className="flex items-start justify-between mb-2 -ml-3">
          <div className="flex items-center space-x-2">
            {getStatusBadge(tutorial.learning_status, tutorial.progress)}
            {getDifficultyBadge(tutorial.difficulty)}
          </div>
          {renderStars(tutorial.rating)}
        </div>
        <CardTitle className="tutorial-card-title text-xl font-bold line-clamp-2 mb-2 min-h-[56px] flex items-start">
          <span className="flex-1">{tutorial.title}</span>
          {tutorial.published_status === 'archived' && (
            <Badge variant="outline" className="ml-2 bg-orange-50 text-orange-600 border-orange-200 text-xs flex-shrink-0">
              已下架
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="tutorial-card-description line-clamp-3 min-h-[54px] ml-0">{tutorial.description}</CardDescription>
      </CardHeader>
      <CardContent className="pt-0 flex-1 flex flex-col tutorial-card-content">
        <div className="space-y-2 flex-1 flex flex-col">
          {/* Progress Bar */}
          {tutorial.learning_status !== "not_started" && (
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>学习进度</span>
                <span>{tutorial.progress}%</span>
              </div>
              <Progress value={tutorial.progress} className="h-2" />
            </div>
          )}

          {/* 上方内容区域 - 自动伸展 */}
          <div className="flex-1 space-y-2">
            {/* Tags - 固定高度 */}
            <div className="min-h-[28px] flex flex-wrap gap-1 -ml-3">
              {tutorial.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
              {tutorial.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{tutorial.tags.length - 3}
                </Badge>
              )}
            </div>

            {/* Meta Information - 固定位置 */}
            <div className="flex items-center justify-between text-sm text-gray-600 min-h-[20px]">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {tutorial.reading_time} 分钟
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {new Date(tutorial.unlocked_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          {/* Action Button - 固定在底部 */}
          <div className="mt-auto">
            <Button className="w-full h-10" onClick={() => router.push(`/tutorial/${tutorial.id}`)}>
              <BookOpen className="h-4 w-4 mr-2" />
              {tutorial.learning_status === "not_started" ? "开始学习" : "继续学习"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const TutorialListItem = ({ tutorial }: { tutorial: Tutorial }) => (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardContent className="p-6 tutorial-list-content">
        <div className="flex items-start space-x-6">
          <div className="flex-1">
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center">
                  <h3 className="tutorial-list-title mb-2">{tutorial.title}</h3>
                  {tutorial.published_status === 'archived' && (
                    <Badge variant="outline" className="ml-3 bg-orange-50 text-orange-600 border-orange-200 text-xs flex-shrink-0">
                      已下架
                    </Badge>
                  )}
                </div>
                <p className="text-gray-600 text-sm line-clamp-2 tutorial-list-description">{tutorial.description}</p>
              </div>
              <div className="flex items-center space-x-2 ml-4">{renderStars(tutorial.rating)}</div>
            </div>

            <div className="flex items-center space-x-3 mb-3">
              {getStatusBadge(tutorial.learning_status, tutorial.progress)}
              {getDifficultyBadge(tutorial.difficulty)}
              <Badge variant="secondary">{tutorial.category_name}</Badge>
            </div>

            {tutorial.learning_status !== "not_started" && (
              <div className="mb-3">
                <div className="flex justify-between text-sm mb-1">
                  <span>学习进度</span>
                  <span>{tutorial.progress}%</span>
                </div>
                <Progress value={tutorial.progress} className="h-2" />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {tutorial.reading_time} 分钟
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  解锁于 {new Date(tutorial.unlocked_at).toLocaleDateString()}
                </div>
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  最后访问 {new Date(tutorial.last_accessed).toLocaleDateString()}
                </div>
              </div>
              <Button onClick={() => router.push(`/tutorial/${tutorial.id}`)}>
                <BookOpen className="h-4 w-4 mr-2" />
                {tutorial.learning_status === "not_started" ? "开始学习" : "继续学习"}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" onClick={() => router.push("/")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回首页
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">我的教程</h1>
                <p className="text-gray-600">管理和学习您已解锁的教程内容</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-sm">
                共 {tutorials.length} 个教程
              </Badge>
              <Badge className="text-sm bg-green-100 text-green-800">
                已完成 {tutorials.filter((t) => t.learning_status === "completed").length} 个
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索教程标题、描述或标签..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name} ({category.count})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as FilterStatus)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="not_started">未开始</SelectItem>
                    <SelectItem value="in_progress">进行中</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="排序" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unlocked_at">解锁时间</SelectItem>
                    <SelectItem value="last_accessed">最后访问</SelectItem>
                    <SelectItem value="title">标题</SelectItem>
                    <SelectItem value="progress">进度</SelectItem>
                    <SelectItem value="rating">评分</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                >
                  {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>

                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tutorial Tabs */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">全部 ({filteredAndSortedTutorials.length})</TabsTrigger>
            <TabsTrigger value="not_started">
              未开始 ({filteredAndSortedTutorials.filter((t) => t.learning_status === "not_started").length})
            </TabsTrigger>
            <TabsTrigger value="in_progress">
              进行中 ({filteredAndSortedTutorials.filter((t) => t.learning_status === "in_progress").length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              已完成 ({filteredAndSortedTutorials.filter((t) => t.learning_status === "completed").length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {filteredAndSortedTutorials.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Unlock className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold mb-2">没有找到匹配的教程</h3>
                  <p className="text-gray-500">尝试调整搜索条件或筛选器</p>
                </CardContent>
              </Card>
            ) : viewMode === "grid" ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredAndSortedTutorials.map((tutorial) => (
                  <TutorialGridCard key={tutorial.id} tutorial={tutorial} />
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredAndSortedTutorials.map((tutorial) => (
                  <TutorialListItem key={tutorial.id} tutorial={tutorial} />
                ))}
              </div>
            )}
          </TabsContent>

          {["not_started", "in_progress", "completed"].map((status) => (
            <TabsContent key={status} value={status}>
              {(() => {
                const statusTutorials = filteredAndSortedTutorials.filter((t) => t.learning_status === status)
                return statusTutorials.length === 0 ? (
                  <Card className="text-center py-12">
                    <CardContent>
                      <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold mb-2">暂无此状态的教程</h3>
                      <p className="text-gray-500">开始学习更多教程吧！</p>
                    </CardContent>
                  </Card>
                ) : viewMode === "grid" ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {statusTutorials.map((tutorial) => (
                      <TutorialGridCard key={tutorial.id} tutorial={tutorial} />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {statusTutorials.map((tutorial) => (
                      <TutorialListItem key={tutorial.id} tutorial={tutorial} />
                    ))}
                  </div>
                )
              })()}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}
