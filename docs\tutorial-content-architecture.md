# 教程内容管理系统架构设计

## 🎯 系统目标

构建灵活、可扩展的教程内容管理系统，支持富媒体内容、版本控制、SEO优化和高性能分发。

## 📋 核心需求

### 功能需求
- **内容编辑**: 富文本编辑器，支持Markdown
- **媒体管理**: 图片、视频上传和管理
- **版本控制**: 草稿/发布状态，历史版本追踪
- **SEO优化**: 元数据管理，结构化数据
- **内容预览**: 编辑预览，移动端适配

### 性能需求
- **加载速度**: 内容页面<2s加载
- **并发支持**: 100+用户同时编辑
- **存储优化**: 媒体文件CDN分发
- **缓存策略**: 多层缓存架构

### 安全需求
- **权限控制**: 基于角色的访问控制
- **内容审核**: 自动+人工审核流程
- **版本回滚**: 误操作恢复机制

## 🏗️ 架构设计

### 数据库架构扩展

```sql
-- 内容版本表
CREATE TABLE IF NOT EXISTS tutorial_versions (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  meta_description VARCHAR(160),
  meta_keywords TEXT[],
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
  author_id VARCHAR(100), -- 作者标识
  editor_id VARCHAR(100), -- 编辑者标识
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(tutorial_id, version_number)
);

-- 媒体资源表
CREATE TABLE IF NOT EXISTS tutorial_media (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(50) NOT NULL, -- image, video, document
  file_size BIGINT NOT NULL,
  storage_path TEXT NOT NULL, -- Supabase Storage路径
  public_url TEXT NOT NULL,
  thumbnail_url TEXT, -- 视频缩略图
  alt_text VARCHAR(255), -- 图片Alt文本
  caption TEXT, -- 媒体说明
  uploaded_by VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容模板表
CREATE TABLE IF NOT EXISTS tutorial_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_content TEXT NOT NULL,
  category VARCHAR(50),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SEO数据表
CREATE TABLE IF NOT EXISTS tutorial_seo (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  slug VARCHAR(255) UNIQUE NOT NULL, -- URL友好标识
  canonical_url TEXT,
  og_title VARCHAR(60),
  og_description VARCHAR(160),
  og_image TEXT,
  structured_data JSONB, -- Schema.org结构化数据
  meta_robots VARCHAR(50) DEFAULT 'index,follow',
  priority DECIMAL(2,1) DEFAULT 0.5, -- Sitemap优先级
  change_frequency VARCHAR(20) DEFAULT 'weekly',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API架构设计

#### 内容管理API端点
```typescript
// 教程内容CRUD
POST   /api/admin/tutorials          // 创建教程
GET    /api/admin/tutorials/:id      // 获取教程详情
PUT    /api/admin/tutorials/:id      // 更新教程
DELETE /api/admin/tutorials/:id      // 删除教程

// 版本管理
GET    /api/admin/tutorials/:id/versions     // 获取版本历史
POST   /api/admin/tutorials/:id/versions     // 创建新版本
PUT    /api/admin/tutorials/:id/versions/:v  // 更新特定版本
POST   /api/admin/tutorials/:id/publish      // 发布版本

// 媒体管理
POST   /api/admin/tutorials/:id/media        // 上传媒体
GET    /api/admin/tutorials/:id/media        // 获取媒体列表
DELETE /api/admin/media/:id                  // 删除媒体

// 内容预览
GET    /api/admin/tutorials/:id/preview      // 预览内容
```

### 前端组件架构

#### 核心组件
```typescript
// 富文本编辑器组件
interface TutorialEditorProps {
  initialContent?: string
  onSave: (content: string) => void
  onAutoSave: (content: string) => void
  mediaUploadUrl: string
  previewMode?: boolean
}

// 媒体管理组件
interface MediaManagerProps {
  tutorialId: number
  allowedTypes: string[]
  maxFileSize: number
  onMediaSelect: (media: MediaFile) => void
}

// 版本控制组件
interface VersionControlProps {
  tutorialId: number
  currentVersion: number
  onVersionSelect: (version: number) => void
  onPublish: (version: number) => void
}
```

### 技术选型

#### 富文本编辑器
- **推荐**: TipTap v2 (基于ProseMirror)
- **优势**: 
  - 现代化React集成
  - 丰富的扩展生态
  - 优秀的性能表现
  - 自定义节点支持

#### 媒体存储
- **方案**: Supabase Storage + CloudFlare CDN
- **配置**:
  - 图片: WebP格式优化，多尺寸缩略图
  - 视频: H.264编码，自适应码率
  - 文档: PDF预览，文本提取

#### 缓存策略
```typescript
// 多层缓存架构
interface CacheStrategy {
  browser: {
    static: '1 year',
    dynamic: '5 minutes'
  },
  cdn: {
    images: '30 days',
    content: '1 hour'
  },
  server: {
    content: '5 minutes',
    metadata: '15 minutes'
  }
}
```

## 🔄 实施计划

### Phase 1: 数据库架构 (1周)
- [ ] 创建版本控制表结构
- [ ] 实现媒体管理表结构  
- [ ] 配置SEO数据表
- [ ] 数据迁移脚本

### Phase 2: 后端API (2周)
- [ ] 实现内容管理API
- [ ] 版本控制逻辑
- [ ] 媒体上传处理
- [ ] SEO数据管理

### Phase 3: 前端组件 (2周)
- [ ] 富文本编辑器集成
- [ ] 媒体管理组件
- [ ] 版本控制界面
- [ ] 内容预览功能

### Phase 4: 优化部署 (1周)
- [ ] 性能优化
- [ ] CDN配置
- [ ] 缓存策略
- [ ] 监控报警

## 📊 成功指标

### 性能指标
- **编辑器响应时间**: <100ms
- **内容加载速度**: <2s
- **媒体上传速度**: 50MB/min
- **缓存命中率**: >80%

### 用户体验指标
- **编辑器易用性**: 用户满意度>4.5/5
- **内容发布成功率**: >99.9%
- **移动端适配**: 完美支持
- **SEO效果**: 搜索排名提升

### 技术指标
- **系统可用性**: 99.9%
- **数据一致性**: 100%
- **备份恢复**: <30分钟
- **扩展性**: 支持10倍流量增长

---

*本架构设计将为知识商城提供企业级的内容管理能力，支撑业务快速发展和用户体验优化。*