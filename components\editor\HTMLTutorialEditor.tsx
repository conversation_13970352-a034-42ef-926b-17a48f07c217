'use client'

import { useState, useCallback, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Code2, 
  Eye, 
  Save,
  AlertTriangle,
  RefreshCw
} from 'lucide-react'
import { TutorialEditor } from './TutorialEditor'

// ==========================================
// HTML支持的教程编辑器
// 支持可视化编辑和HTML源码编辑
// 实时预览HTML渲染效果
// ==========================================

interface HTMLTutorialEditorProps {
  initialContent?: string
  onSave?: (content: string) => void
  onAutoSave?: (content: string) => void
  className?: string
  placeholder?: string
  editable?: boolean
}

export function HTMLTutorialEditor({
  initialContent = '',
  onSave,
  onAutoSave,
  className = '',
  placeholder = '开始编写你的教程内容...',
  editable = true
}: HTMLTutorialEditorProps) {
  const [content, setContent] = useState(initialContent)
  const [htmlSource, setHtmlSource] = useState(initialContent)
  const [activeTab, setActiveTab] = useState<'visual' | 'html' | 'preview'>('visual')
  const [isSaving, setIsSaving] = useState(false)
  const [htmlError, setHtmlError] = useState<string | null>(null)

  // 监听 initialContent 变化，用于编辑模式下的内容加载
  useEffect(() => {
    if (initialContent !== content) {
      setContent(initialContent)
      setHtmlSource(initialContent)
    }
  }, [initialContent])

  // 同步内容 - 确保双向同步
  useEffect(() => {
    if (activeTab === 'html') {
      setHtmlSource(content)
    }
  }, [content, activeTab])

  // 验证HTML
  const validateHTML = useCallback((html: string) => {
    try {
      // 创建临时DOM来验证HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html
      setHtmlError(null)
      return true
    } catch (error) {
      setHtmlError('HTML语法错误，请检查标签是否正确闭合')
      return false
    }
  }, [])

  // 处理tab切换时的状态同步
  const handleTabChange = useCallback((newTab: 'visual' | 'html' | 'preview') => {
    // 从HTML模式切换出来时，同步HTML内容到主状态
    if (activeTab === 'html' && newTab !== 'html') {
      if (validateHTML(htmlSource)) {
        setContent(htmlSource)
        if (onAutoSave) {
          onAutoSave(htmlSource)
        }
      }
    }
    setActiveTab(newTab)
  }, [activeTab, htmlSource, validateHTML, onAutoSave])

  // 从HTML源码更新内容
  const updateFromHTML = useCallback(() => {
    if (validateHTML(htmlSource)) {
      setContent(htmlSource)
      if (onAutoSave) {
        onAutoSave(htmlSource)
      }
    }
  }, [htmlSource, validateHTML, onAutoSave])

  // 手动保存
  const handleSave = useCallback(async () => {
    if (!onSave) return
    
    setIsSaving(true)
    try {
      // 如果当前在HTML模式，先同步HTML内容到主状态
      let finalContent = content
      if (activeTab === 'html') {
        if (validateHTML(htmlSource)) {
          setContent(htmlSource)
          finalContent = htmlSource
        } else {
          throw new Error('HTML格式错误，无法保存')
        }
      }
      
      await onSave(finalContent)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }, [content, htmlSource, activeTab, onSave, validateHTML])

  // 处理可视化编辑器的内容变化
  const handleVisualEditorChange = useCallback((newContent: string) => {
    setContent(newContent)
    if (onAutoSave) {
      onAutoSave(newContent)
    }
  }, [onAutoSave])

  // 处理HTML源码变化
  const handleHTMLChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newHTML = e.target.value
    setHtmlSource(newHTML)
    validateHTML(newHTML)
  }, [validateHTML])

  // 插入常用HTML模板
  const insertHTMLTemplate = useCallback((template: string) => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newValue = htmlSource.substring(0, start) + template + htmlSource.substring(end)
      setHtmlSource(newValue)
      
      // 重新聚焦并设置光标位置
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + template.length, start + template.length)
      }, 0)
    }
  }, [htmlSource])

  const htmlTemplates = {
    section: `<section data-section="1" data-section-title="章节标题" data-estimated-time="10">
  <h2>章节标题</h2>
  <p>章节内容...</p>
</section>`,
    checkpoint: `<div data-checkpoint="1-1" data-checkpoint-type="knowledge" data-points="15">
  <h4>🎯 检查点：知识验证</h4>
  <p>检查点内容...</p>
</div>`,
    interactive: `<div data-interactive="exercise" data-required="true">
  <h4>📝 互动练习</h4>
  <p>练习内容...</p>
</div>`,
    tip: `<div class="tip">
  <strong>💡 提示：</strong>
  <p>提示内容...</p>
</div>`,
    warning: `<div class="warning">
  <strong>⚠️ 注意：</strong>
  <p>警告内容...</p>
</div>`,
    code: `<pre><code class="language-javascript">
// 代码示例
function example() {
  console.log('Hello World!');
}
</code></pre>`
  }

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* 标签页导航 */}
      <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as any)} className="w-full">
        <div className="flex items-center justify-between p-2 bg-gray-50 border-b border-gray-200">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="visual" className="flex items-center">
              <Eye className="h-4 w-4 mr-2" />
              可视化
            </TabsTrigger>
            <TabsTrigger value="html" className="flex items-center">
              <Code2 className="h-4 w-4 mr-2" />
              HTML源码
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center">
              <RefreshCw className="h-4 w-4 mr-2" />
              预览效果
            </TabsTrigger>
          </TabsList>

          {/* 保存按钮 */}
          {onSave && (
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleSave}
              disabled={isSaving}
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? '保存中...' : '保存'}
            </Button>
          )}
        </div>

        {/* 可视化编辑器 */}
        <TabsContent value="visual" className="m-0">
          <TutorialEditor
            initialContent={content}
            onSave={handleSave}
            onAutoSave={handleVisualEditorChange}
            placeholder={placeholder}
            editable={editable}
            className="border-0"
          />
        </TabsContent>

        {/* HTML源码编辑器 */}
        <TabsContent value="html" className="m-0">
          <div className="flex flex-col">
            {/* HTML工具栏 */}
            <div className="flex items-center gap-1 p-2 bg-gray-50 border-b border-gray-200 flex-wrap">
              <span className="text-sm font-medium text-gray-700 mr-2">快速插入：</span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.section)}
              >
                章节
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.checkpoint)}
              >
                检查点
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.interactive)}
              >
                互动练习
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.tip)}
              >
                提示
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.warning)}
              >
                警告
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => insertHTMLTemplate(htmlTemplates.code)}
              >
                代码块
              </Button>

              <Separator orientation="vertical" className="h-6 mx-2" />
              
              <Button
                variant="outline"
                size="sm"
                onClick={updateFromHTML}
                disabled={!!htmlError}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                应用更改
              </Button>
            </div>

            {/* HTML错误提示 */}
            {htmlError && (
              <Alert className="m-2 border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-red-800">
                  {htmlError}
                </AlertDescription>
              </Alert>
            )}

            {/* HTML编辑区域 */}
            <textarea
              value={htmlSource}
              onChange={handleHTMLChange}
              className="w-full h-96 p-4 font-mono text-sm border-0 resize-none focus:outline-none"
              placeholder="输入HTML代码..."
              spellCheck={false}
            />
          </div>
        </TabsContent>

        {/* 预览模式 */}
        <TabsContent value="preview" className="m-0">
          <div className="p-6">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <Eye className="h-4 w-4 inline mr-2" />
                预览模式：显示HTML渲染后的实际效果
              </p>
            </div>
            
            {/* HTML内容预览 */}
            <div 
              className="tutorial-content prose max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100"
              dangerouslySetInnerHTML={{ __html: htmlSource || content }}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* 底部状态栏 */}
      <div className="flex justify-between items-center p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
        <div>
          当前模式: {activeTab === 'visual' ? '可视化编辑' : activeTab === 'html' ? 'HTML源码' : 'HTML预览'}
        </div>
        <div>
          支持HTML标签和自定义属性 | 可实时预览效果
        </div>
      </div>
    </div>
  )
}