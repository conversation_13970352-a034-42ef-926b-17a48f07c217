# 教程内容格式规范

## 进度跟踪标记系统

为了让学习进度系统能够准确跟踪用户的学习状态，教程内容需要使用特定的HTML标记格式。

### 1. 章节标记
使用 `data-section` 属性标记每个学习章节：

```html
<section data-section="1" data-section-title="设计系统基础" data-estimated-time="15">
  <h2>第一章：设计系统基础</h2>
  <!-- 章节内容 -->
</section>
```

### 2. 进度检查点
使用 `data-checkpoint` 标记重要的学习检查点：

```html
<div data-checkpoint="1-1" data-checkpoint-type="knowledge" data-points="10">
  <h3>🎯 检查点：设计系统的核心概念</h3>
  <p>完成这个部分后，你应该理解设计系统的基本概念和作用。</p>
</div>
```

### 3. 互动元素
使用 `data-interactive` 标记需要用户互动的元素：

```html
<div data-interactive="exercise" data-required="true">
  <h4>📝 实践练习</h4>
  <p>请根据学到的知识，设计一个简单的按钮组件。</p>
</div>
```

### 4. 学习目标
使用 `data-learning-objective` 标记学习目标：

```html
<div data-learning-objective="primary">
  <h3>🎯 学习目标</h3>
  <ul>
    <li>理解设计系统的基本概念</li>
    <li>掌握设计令牌的使用方法</li>
  </ul>
</div>
```

## 进度计算逻辑

1. **章节完成度**：根据 `data-section` 标记的章节数量计算
2. **检查点完成**：用户需要通过所有 `data-checkpoint` 标记的检查点
3. **互动元素参与**：追踪用户与 `data-interactive` 元素的交互
4. **时间追踪**：基于 `data-estimated-time` 计算预期学习时间

## 状态映射

- `not_started`: 未开始任何章节
- `in_progress`: 已开始但未完成所有章节
- `completed`: 完成所有章节和检查点