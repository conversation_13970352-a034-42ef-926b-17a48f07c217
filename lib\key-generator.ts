import crypto from "crypto"

// 生成唯一密钥
export function generateUniqueKey(): string {
  // 生成32位随机字符串
  const timestamp = Date.now().toString(36)
  const randomBytes = crypto.randomBytes(16).toString("hex")
  const combined = timestamp + randomBytes

  // 使用SHA-256哈希并截取前24位
  const hash = crypto.createHash("sha256").update(combined).digest("hex")
  return hash.substring(0, 24).toUpperCase()
}

// 批量生成密钥
export function generateBatchKeys(count: number): string[] {
  const keys = new Set<string>()

  while (keys.size < count) {
    keys.add(generateUniqueKey())
  }

  return Array.from(keys)
}

// 验证密钥格式
export function validateKeyFormat(key: string): boolean {
  // 检查密钥格式：24位大写字母和数字
  const keyRegex = /^[A-Z0-9]{24}$/
  return keyRegex.test(key)
}
