-- ==========================================
-- Row Level Security (RLS) 策略配置
-- 基于最小权限原则和深度防御策略
-- ==========================================

-- 步骤 1: 启用所有表的 RLS
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutorials ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutorial_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_unlocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;

-- 步骤 2: 删除可能存在的旧策略
DROP POLICY IF EXISTS "categories_public_read" ON categories;
DROP POLICY IF EXISTS "tutorials_public_read" ON tutorials;
DROP POLICY IF EXISTS "tutorial_keys_admin_only" ON tutorial_keys;
DROP POLICY IF EXISTS "user_unlocks_owner_access" ON user_unlocks;
DROP POLICY IF EXISTS "system_config_admin_only" ON system_config;
DROP POLICY IF EXISTS "service_role_bypass_all" ON categories;
DROP POLICY IF EXISTS "service_role_bypass_all" ON tutorials;
DROP POLICY IF EXISTS "service_role_bypass_all" ON tutorial_keys;
DROP POLICY IF EXISTS "service_role_bypass_all" ON user_unlocks;
DROP POLICY IF EXISTS "service_role_bypass_all" ON system_config;

-- 步骤 3: 创建分层访问策略

-- ===================
-- 分类表 (categories)
-- ===================

-- 公开读取策略：所有用户可读取分类
CREATE POLICY "categories_public_read" ON categories
    FOR SELECT 
    USING (true);

-- 服务角色完全访问
CREATE POLICY "categories_service_access" ON categories
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================
-- 教程表 (tutorials)
-- ===================

-- 公开读取策略：仅已发布教程可公开访问
CREATE POLICY "tutorials_public_read" ON tutorials
    FOR SELECT 
    USING (status = 'published');

-- 服务角色完全访问
CREATE POLICY "tutorials_service_access" ON tutorials
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================
-- 密钥表 (tutorial_keys)
-- ===================

-- 严格限制：仅服务角色可访问密钥表
CREATE POLICY "tutorial_keys_service_only" ON tutorial_keys
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================
-- 用户解锁记录表 (user_unlocks)
-- ===================

-- 用户只能查看自己的解锁记录
CREATE POLICY "user_unlocks_owner_read" ON user_unlocks
    FOR SELECT 
    USING (
        -- 基于用户标识符的访问控制
        user_identifier = (
            SELECT user_identifier_from_headers()
        )
    );

-- 服务角色完全访问（用于验证和创建记录）
CREATE POLICY "user_unlocks_service_access" ON user_unlocks
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================
-- 系统配置表 (system_config)
-- ===================

-- 严格限制：仅服务角色可访问
CREATE POLICY "system_config_service_only" ON system_config
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================
-- 辅助函数
-- ===================

-- 创建辅助函数：从请求头获取用户标识符
-- 注意：这是一个简化版本，生产环境需要更复杂的实现
CREATE OR REPLACE FUNCTION user_identifier_from_headers()
RETURNS TEXT AS $$
BEGIN
    -- 简化实现：返回当前用户ID或匿名标识
    -- 实际实现需要基于请求头计算用户标识符
    RETURN COALESCE(auth.uid()::text, 'anonymous');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================
-- 验证策略
-- ===================

-- 查看所有策略
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;