-- 创建媒体文件管理表
CREATE TABLE IF NOT EXISTS tutorial_media (
  id SERIAL PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL UNIQUE,
  file_type VARCHAR(100) NOT NULL,
  file_size INTEGER NOT NULL,
  public_url TEXT NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tutorial_media_uploaded_at ON tutorial_media(uploaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_tutorial_media_file_type ON tutorial_media(file_type);
CREATE INDEX IF NOT EXISTS idx_tutorial_media_is_active ON tutorial_media(is_active);

-- 设置RLS策略
ALTER TABLE tutorial_media ENABLE ROW LEVEL SECURITY;

-- 允许所有人查看已激活的媒体文件
CREATE POLICY "Anyone can view active media files" ON tutorial_media
  FOR SELECT USING (is_active = true);

-- 只有服务角色可以插入、更新、删除
CREATE POLICY "Service role can manage media files" ON tutorial_media
  FOR ALL USING (auth.role() = 'service_role');