import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { getUserIdentifier } from "@/lib/auth"

// 获取当前有效的公告（用户端）
export async function GET(request: NextRequest) {
  try {
    const userIdentifier = getUserIdentifier(request)
    const { searchParams } = new URL(request.url)
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)

    const currentTime = new Date().toISOString()

    // 获取当前有效的公告
    const { data: announcements, error } = await supabaseAdmin
      .from('announcements')
      .select(`
        id,
        title,
        content,
        type,
        priority,
        start_date,
        end_date,
        target_audience,
        created_at
      `)
      .eq('is_active', true)
      .lte('start_date', currentTime)
      .or(`end_date.is.null,end_date.gte.${currentTime}`)
      .in('target_audience', ['all', 'users'])
      .order('created_at', { ascending: false })
      .order('priority', { ascending: false })
      .limit(limit)

    if (error) {
      console.error("获取公告错误:", error)
      return NextResponse.json({ error: "获取公告失败" }, { status: 500 })
    }

    // 获取用户已读记录
    const announcementIds = announcements.map(a => a.id)
    let readRecords = []
    
    if (announcementIds.length > 0) {
      const { data: reads } = await supabaseAdmin
        .from('announcement_reads')
        .select('announcement_id')
        .eq('user_identifier', userIdentifier)
        .in('announcement_id', announcementIds)
      
      readRecords = reads || []
    }

    const readAnnouncementIds = new Set(readRecords.map(r => r.announcement_id))

    // 标记已读状态
    const announcementsWithReadStatus = announcements.map(announcement => ({
      ...announcement,
      is_read: readAnnouncementIds.has(announcement.id)
    }))

    // 统计未读数量
    const unreadCount = announcementsWithReadStatus.filter(a => !a.is_read).length

    return NextResponse.json({
      success: true,
      data: {
        announcements: announcementsWithReadStatus,
        unread_count: unreadCount
      }
    })

  } catch (error) {
    console.error("公告API异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}

// 标记公告为已读
export async function POST(request: NextRequest) {
  try {
    const userIdentifier = getUserIdentifier(request)
    const { announcement_id } = await request.json()

    if (!announcement_id) {
      return NextResponse.json(
        { error: "公告ID是必填字段" }, 
        { status: 400 }
      )
    }

    // 检查公告是否存在且有效
    const { data: announcement, error: checkError } = await supabaseAdmin
      .from('announcements')
      .select('id')
      .eq('id', announcement_id)
      .eq('is_active', true)
      .single()

    if (checkError || !announcement) {
      return NextResponse.json({ error: "公告不存在或已失效" }, { status: 404 })
    }

    // 插入已读记录（如果已存在则忽略）
    const { error: insertError } = await supabaseAdmin
      .from('announcement_reads')
      .upsert({
        announcement_id,
        user_identifier: userIdentifier,
        read_at: new Date().toISOString()
      }, {
        onConflict: 'announcement_id,user_identifier'
      })

    if (insertError) {
      console.error("标记已读错误:", insertError)
      return NextResponse.json({ error: "标记已读失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: "已标记为已读"
    })

  } catch (error) {
    console.error("标记已读异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}