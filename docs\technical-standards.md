# 技术实施规范

## 概述

本文档为知识商城项目的技术实施提供详细规范，包括代码标准、架构模式、安全要求等。

---

## 代码规范

### TypeScript 规范
```typescript
// 接口定义规范
interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_id: number
  tags: string[]
  status: 'draft' | 'published' | 'archived'
  price: number
  created_at: string
  updated_at: string
}

// 使用严格的类型检查
const createTutorial = async (data: Omit<Tutorial, 'id' | 'created_at' | 'updated_at'>): Promise<Tutorial> => {
  // 实现逻辑
}
```

### API 路由规范
```typescript
// 标准错误处理
export async function GET(request: NextRequest) {
  try {
    // 业务逻辑
    return NextResponse.json(data)
  } catch (error) {
    console.error('API Error:', error)
    
    // 统一错误响应格式
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
```

### 数据库操作规范
```typescript
// 使用 Supabase 客户端的标准模式
export async function getTutorialsByCategory(categoryId: number) {
  const { data, error } = await supabaseAdmin
    .from('tutorials')
    .select(`
      *,
      categories!inner(name)
    `)
    .eq('category_id', categoryId)
    .eq('status', 'published')
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch tutorials: ${error.message}`)
  }

  return data
}
```

---

## 架构模式

### 文件结构规范
```
app/
├── (auth)/          # 认证相关页面
├── admin/           # 管理后台
├── api/             # API 接口
│   ├── auth/        # 认证接口
│   ├── admin/       # 管理员接口
│   ├── public/      # 公开接口
│   └── user/        # 用户接口
├── globals.css      # 全局样式
└── layout.tsx       # 根布局

lib/
├── supabase.ts      # 数据库客户端
├── auth.ts          # 认证工具
├── utils.ts         # 通用工具
├── validations.ts   # 数据验证
└── constants.ts     # 常量定义

components/
├── ui/              # 基础UI组件
├── forms/           # 表单组件
├── layouts/         # 布局组件
└── features/        # 功能组件
```

### 组件设计规范
```typescript
// 组件 Props 接口定义
interface TutorialCardProps {
  tutorial: Tutorial
  isUnlocked?: boolean
  onAction?: (tutorial: Tutorial) => void
  className?: string
}

// 使用 forwardRef 支持 ref 传递
const TutorialCard = forwardRef<HTMLDivElement, TutorialCardProps>(
  ({ tutorial, isUnlocked = false, onAction, className }, ref) => {
    return (
      <Card ref={ref} className={cn("tutorial-card", className)}>
        {/* 组件内容 */}
      </Card>
    )
  }
)

TutorialCard.displayName = "TutorialCard"
```

---

## 安全规范

### 认证和授权
```typescript
// 中间件函数
export async function requireAuth(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')
  
  if (!token) {
    throw new Error('Authentication required')
  }

  const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)
  
  if (error || !user) {
    throw new Error('Invalid authentication token')
  }

  return user
}

// 管理员权限检查
export async function requireAdmin(request: NextRequest) {
  const user = await requireAuth(request)
  
  const { data: profile } = await supabaseAdmin
    .from('user_profiles')
    .select('role')
    .eq('user_id', user.id)
    .single()
  
  if (profile?.role !== 'admin') {
    throw new Error('Admin access required')
  }
  
  return user
}
```

### 数据验证
```typescript
import { z } from 'zod'

// 创建教程的验证模式
export const createTutorialSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题长度不能超过200字符'),
  description: z.string().min(1, '描述不能为空').max(500, '描述长度不能超过500字符'),
  content: z.string().min(1, '内容不能为空'),
  category_id: z.number().int().positive('请选择有效的分类'),
  tags: z.array(z.string()).max(10, '标签数量不能超过10个'),
  price: z.number().min(0, '价格不能为负数').max(9999.99, '价格不能超过9999.99'),
  status: z.enum(['draft', 'published', 'archived'])
})

// 在 API 中使用
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createTutorialSchema.parse(body)
    
    // 处理业务逻辑
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }
    throw error
  }
}
```

### Rate Limiting
```typescript
// 基于 IP 的速率限制
const rateLimitMap = new Map()

export function rateLimit(ip: string, limit: number = 100, window: number = 3600000) {
  const now = Date.now()
  const windowStart = now - window
  
  if (!rateLimitMap.has(ip)) {
    rateLimitMap.set(ip, [])
  }
  
  const requests = rateLimitMap.get(ip).filter((time: number) => time > windowStart)
  
  if (requests.length >= limit) {
    throw new Error('Rate limit exceeded')
  }
  
  requests.push(now)
  rateLimitMap.set(ip, requests)
}
```

---

## 性能优化规范

### 数据库查询优化
```typescript
// 使用索引优化的查询
export async function getPopularTutorials(limit: number = 10) {
  // 确保在数据库中创建了相应的索引
  const { data, error } = await supabaseAdmin
    .from('tutorials')
    .select(`
      id,
      title,
      description,
      price,
      categories(name),
      tutorial_keys(status)
    `)
    .eq('status', 'published')
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) throw error
  return data
}

// 避免 N+1 查询问题
export async function getTutorialsWithUnlockStatus(userId: string) {
  const { data, error } = await supabaseAdmin
    .from('tutorials')
    .select(`
      *,
      categories(name),
      user_unlocks!left(user_id)
    `)
    .eq('status', 'published')
    .eq('user_unlocks.user_id', userId)

  if (error) throw error
  return data
}
```

### 前端性能优化
```typescript
// 使用 React.memo 优化组件重渲染
export const TutorialCard = React.memo<TutorialCardProps>(({ tutorial, isUnlocked }) => {
  // 组件实现
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return prevProps.tutorial.id === nextProps.tutorial.id &&
         prevProps.isUnlocked === nextProps.isUnlocked
})

// 使用 useMemo 优化计算
const TutorialList = ({ tutorials, searchQuery }: TutorialListProps) => {
  const filteredTutorials = useMemo(() => {
    return tutorials.filter(tutorial =>
      tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutorial.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [tutorials, searchQuery])

  return (
    <div>
      {filteredTutorials.map(tutorial => (
        <TutorialCard key={tutorial.id} tutorial={tutorial} />
      ))}
    </div>
  )
}
```

### 图片优化
```typescript
// 使用 Next.js Image 组件
import Image from 'next/image'

const TutorialThumbnail = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={400}
      height={225}
      priority={false}
      loading="lazy"
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      {...props}
    />
  )
}
```

---

## 错误处理规范

### 全局错误处理
```typescript
// app/error.tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 错误日志记录
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="error-container">
      <h2>出现了一些问题</h2>
      <p>请稍后重试或联系技术支持</p>
      <button onClick={reset}>重试</button>
    </div>
  )
}
```

### API 错误处理
```typescript
// lib/api-error.ts
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 错误处理中间件
export function handleApiError(error: unknown) {
  if (error instanceof ApiError) {
    return NextResponse.json(
      { error: error.message, code: error.code },
      { status: error.statusCode }
    )
  }

  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { error: 'Validation failed', details: error.errors },
      { status: 400 }
    )
  }

  // 未知错误
  console.error('Unhandled API error:', error)
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
```

---

## 测试规范

### 单元测试
```typescript
// __tests__/lib/key-generator.test.ts
import { generateUniqueKey, validateKeyFormat } from '@/lib/key-generator'

describe('Key Generator', () => {
  test('should generate 24-character key', () => {
    const key = generateUniqueKey()
    expect(key).toHaveLength(24)
  })

  test('should generate unique keys', () => {
    const key1 = generateUniqueKey()
    const key2 = generateUniqueKey()
    expect(key1).not.toBe(key2)
  })

  test('should validate key format correctly', () => {
    expect(validateKeyFormat('ABC123DEF456GHI789JKL012')).toBe(true)
    expect(validateKeyFormat('invalid-key')).toBe(false)
    expect(validateKeyFormat('TOO_SHORT')).toBe(false)
  })
})
```

### 集成测试
```typescript
// __tests__/api/tutorials.test.ts
import { NextRequest } from 'next/server'
import { GET } from '@/app/api/public/tutorials/route'

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockResolvedValue({
      data: [
        { id: 1, title: 'Test Tutorial', status: 'published' }
      ],
      error: null
    })
  }
}))

describe('/api/public/tutorials', () => {
  test('should return published tutorials', async () => {
    const request = new NextRequest('http://localhost:3000/api/public/tutorials')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(Array.isArray(data)).toBe(true)
  })
})
```

---

## 监控和日志规范

### 错误监控
```typescript
// lib/monitoring.ts
export function logError(error: Error, context?: Record<string, any>) {
  const errorData = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context
  }

  // 开发环境：控制台输出
  if (process.env.NODE_ENV === 'development') {
    console.error('Error logged:', errorData)
  }

  // 生产环境：发送到监控服务
  if (process.env.NODE_ENV === 'production') {
    // 集成 Sentry 或其他监控服务
    // Sentry.captureException(error, { extra: context })
  }
}
```

### 性能监控
```typescript
// lib/performance.ts
export function trackPerformance(operation: string, startTime: number) {
  const duration = Date.now() - startTime
  
  console.log(`Performance: ${operation} took ${duration}ms`)
  
  // 生产环境中发送到分析服务
  if (process.env.NODE_ENV === 'production' && duration > 1000) {
    // 记录慢查询
    logError(new Error(`Slow operation: ${operation}`), { duration })
  }
}

// 使用示例
export async function getTutorials() {
  const startTime = Date.now()
  
  try {
    const data = await supabaseAdmin.from('tutorials').select('*')
    trackPerformance('getTutorials', startTime)
    return data
  } catch (error) {
    logError(error as Error, { operation: 'getTutorials' })
    throw error
  }
}
```

---

## 部署规范

### 环境变量管理
```bash
# .env.example
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# 可选：第三方服务
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id
```

### 构建优化
```javascript
// next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 生产环境配置
  eslint: {
    ignoreDuringBuilds: false, // 启用构建时检查
  },
  typescript: {
    ignoreBuildErrors: false, // 启用类型检查
  },
  images: {
    domains: ['your-domain.com'], // 允许的图片域名
    unoptimized: false, // 启用图片优化
  },
  // 输出配置
  output: 'standalone', // 支持 Docker 部署
  // 压缩配置
  compress: true,
  // 安全头
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ]
  },
}

export default nextConfig
```

---

## 代码审查清单

### 功能审查
- [ ] 功能是否按需求正确实现
- [ ] 错误处理是否完善
- [ ] 用户体验是否友好
- [ ] 性能是否满足要求

### 代码质量审查
- [ ] 代码是否遵循项目规范
- [ ] 变量和函数命名是否清晰
- [ ] 是否有重复代码
- [ ] 注释是否充分且准确

### 安全审查
- [ ] 是否有 SQL 注入风险
- [ ] 用户输入是否经过验证
- [ ] 敏感信息是否得到保护
- [ ] 权限控制是否正确

### 测试审查
- [ ] 单元测试覆盖率是否足够
- [ ] 边界条件是否被测试
- [ ] 集成测试是否通过
- [ ] 性能预期是否达标

---

*本文档将随着项目发展持续更新和完善。*