import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { validateKeyFormat, getUserIdentifier, checkRateLimit } from "@/lib/auth"

export async function POST(request: NextRequest) {
  try {
    console.log("🔑 开始密钥验证流程")
    
    const { key } = await request.json()

    // 验证密钥格式
    if (!validateKeyFormat(key)) {
      console.log("❌ 密钥格式不正确:", key)
      return NextResponse.json({ error: "密钥格式不正确" }, { status: 400 })
    }

    // 获取用户标识
    const userIdentifier = getUserIdentifier(request)
    console.log("👤 用户标识符:", userIdentifier)

    // 检查频率限制
    const rateLimitOk = await checkRateLimit(userIdentifier, 3600000, 10) // 1小时内最多10次
    if (!rateLimitOk) {
      console.log("⚠️ 用户超出频率限制:", userIdentifier)
      return NextResponse.json({ error: "验证次数过多，请稍后再试" }, { status: 429 })
    }

    // 使用 Supabase 事务验证密钥并解锁教程
    const { data: keyData, error: keyError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        *,
        tutorials!inner (
          id, title, description, content, price, status, tags,
          categories (name)
        )
      `)
      .eq('key_code', key.toUpperCase())
      .eq('status', 'unused')
      .eq('tutorials.status', 'published')
      .single()

    if (keyError || !keyData) {
      console.log("❌ 密钥无效或已被使用:", keyError)
      return NextResponse.json({ error: "无效的密钥或密钥已被使用" }, { status: 400 })
    }

    console.log("✅ 找到有效密钥:", keyData.key_code)

    // 检查用户是否已经解锁过这个教程
    const { data: existingUnlock } = await supabaseAdmin
      .from('user_unlocks')
      .select('id')
      .eq('user_identifier', userIdentifier)
      .eq('tutorial_id', keyData.tutorial_id)
      .single()

    if (existingUnlock) {
      console.log("⚠️ 用户已解锁过此教程:", keyData.tutorial_id)
      return NextResponse.json({ error: "您已经解锁过这个教程了" }, { status: 400 })
    }

    // 开始事务：标记密钥为已使用并记录用户解锁
    try {
      // 1. 标记密钥为已使用
      const { error: updateKeyError } = await supabaseAdmin
        .from('tutorial_keys')
        .update({
          status: 'used',
          used_at: new Date().toISOString(),
          user_identifier: userIdentifier
        })
        .eq('id', keyData.id)

      if (updateKeyError) {
        throw new Error(`更新密钥状态失败: ${updateKeyError.message}`)
      }

      // 2. 记录用户解锁
      const { error: unlockError } = await supabaseAdmin
        .from('user_unlocks')
        .insert({
          user_identifier: userIdentifier,
          tutorial_id: keyData.tutorial_id,
          key_id: keyData.id,
          ip_address: request.ip || request.headers.get("x-forwarded-for") || "unknown",
          user_agent: request.headers.get("user-agent") || "unknown",
          unlocked_at: new Date().toISOString()
        })

      if (unlockError) {
        // 回滚密钥状态
        await supabaseAdmin
          .from('tutorial_keys')
          .update({
            status: 'unused',
            used_at: null,
            user_identifier: null
          })
          .eq('id', keyData.id)
        
        throw new Error(`记录用户解锁失败: ${unlockError.message}`)
      }

      console.log("✅ 密钥验证成功，教程已解锁")

      // 构建返回的教程数据
      const tutorial = keyData.tutorials
      const tutorialResult = {
        id: tutorial.id,
        title: tutorial.title,
        description: tutorial.description,
        content: tutorial.content,
        category_name: tutorial.categories?.name || null,
        tags: tutorial.tags || [],
        price: tutorial.price,
      }

      // 获取用户所有已解锁的教程
      const { data: userUnlocks } = await supabaseAdmin
        .from('user_unlocks')
        .select(`
          tutorials (
            id, title, description, price, tags,
            categories (name)
          )
        `)
        .eq('user_identifier', userIdentifier)
        .order('unlocked_at', { ascending: false })

      const userUnlocksFormatted = userUnlocks?.map(unlock => ({
        id: unlock.tutorials.id,
        title: unlock.tutorials.title,
        description: unlock.tutorials.description,
        price: unlock.tutorials.price,
        category_name: unlock.tutorials.categories?.name || null,
        tags: unlock.tutorials.tags || []
      })) || []

      return NextResponse.json({
        tutorial: tutorialResult,
        user_unlocks: userUnlocksFormatted,
      })

    } catch (transactionError) {
      console.error("❌ 事务执行失败:", transactionError)
      throw transactionError
    }

  } catch (error) {
    console.error("❌ 密钥验证异常:", error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "密钥验证失败",
      },
      { status: 400 },
    )
  }
}
