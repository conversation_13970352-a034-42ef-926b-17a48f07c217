# 微信扫码登录API接口文档

## 概述

微信扫码登录是基于OAuth2.0协议标准构建的微信OAuth2.0授权登录系统。用户可以通过微信扫码的方式快速登录第三方网站或应用，无需注册新账号。

## 前置条件

1. 在[微信开放平台](https://open.weixin.qq.com/)注册开发者账号
2. 创建并审核通过网站应用
3. 获得相应的AppID和AppSecret
4. 申请微信登录功能并通过审核

## 授权流程

微信OAuth2.0授权登录采用authorization_code模式，整体流程如下：

1. 第三方发起微信授权登录请求，用户扫码授权
2. 微信返回授权临时票据code
3. 通过code换取access_token
4. 使用access_token调用接口获取用户信息

## API接口详情

### 1. 获取授权码（Code）

#### 跳转授权方式

**接口地址：**
```
https://open.weixin.qq.com/connect/qrconnect
```

**请求方式：** GET

**参数说明：**

| 参数 | 必须 | 说明 |
|------|------|------|
| appid | 是 | 应用唯一标识 |
| redirect_uri | 是 | 授权后重定向的回调链接地址，需要进行urlEncode |
| response_type | 是 | 填写code |
| scope | 是 | 应用授权作用域，网页应用填写snsapi_login |
| state | 否 | 用于保持请求和回调的状态，建议使用随机数加session校验 |
| lang | 否 | 界面语言，支持cn（中文简体）与en（英文），默认为cn |

**请求示例：**
```
https://open.weixin.qq.com/connect/qrconnect?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect
```

**返回说明：**
用户授权后，将重定向到redirect_uri，并带上code和state参数：
```
redirect_uri?code=CODE&state=STATE
```

#### 内嵌二维码方式

**引入JS文件：**
```html
<script src="http://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
```

**JS实现：**
```javascript
var obj = new WxLogin({
    self_redirect: true,
    id: "login_container", 
    appid: "YOUR_APPID", 
    scope: "snsapi_login", 
    redirect_uri: "YOUR_REDIRECT_URI",
    state: "YOUR_STATE",
    style: "black", // 或 "white"
    href: "", // 自定义样式链接
    stylelite: 1, // 新版UI样式
    fast_login: 1, // 启用快速登录
    color_scheme: "auto", // 主题：light/dark/auto
    onReady: function(isReady) {
        console.log("二维码加载状态:", isReady);
    },
    onQRcodeReady: function() {
        console.log("二维码加载完成");
    }
});
```

### 2. 通过Code获取Access Token

**接口地址：**
```
https://api.weixin.qq.com/sns/oauth2/access_token
```

**请求方式：** GET

**参数说明：**

| 参数 | 必须 | 说明 |
|------|------|------|
| appid | 是 | 应用唯一标识 |
| secret | 是 | 应用密钥AppSecret |
| code | 是 | 第一步获取的code参数 |
| grant_type | 是 | 填写authorization_code |

**请求示例：**
```
https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code
```

**成功返回：**
```json
{
    "access_token": "ACCESS_TOKEN",
    "expires_in": 7200,
    "refresh_token": "REFRESH_TOKEN",
    "openid": "OPENID",
    "scope": "SCOPE",
    "unionid": "UNIONID"
}
```

**参数说明：**

| 参数 | 说明 |
|------|------|
| access_token | 接口调用凭证 |
| expires_in | access_token超时时间，单位（秒） |
| refresh_token | 用户刷新access_token |
| openid | 授权用户唯一标识 |
| scope | 用户授权的作用域 |
| unionid | 用户统一标识，同一开放平台账号下唯一 |

**错误返回：**
```json
{
    "errcode": 40029,
    "errmsg": "invalid code"
}
```

### 3. 刷新Access Token

**接口地址：**
```
https://api.weixin.qq.com/sns/oauth2/refresh_token
```

**请求方式：** GET

**参数说明：**

| 参数 | 必须 | 说明 |
|------|------|------|
| appid | 是 | 应用唯一标识 |
| grant_type | 是 | 填写refresh_token |
| refresh_token | 是 | 通过access_token获取到的refresh_token |

**请求示例：**
```
https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=APPID&grant_type=refresh_token&refresh_token=REFRESH_TOKEN
```

### 4. 验证Access Token

**接口地址：**
```
https://api.weixin.qq.com/sns/auth
```

**请求方式：** GET

**参数说明：**

| 参数 | 必须 | 说明 |
|------|------|------|
| access_token | 是 | 调用接口凭证 |
| openid | 是 | 用户标识 |

**成功返回：**
```json
{
    "errcode": 0,
    "errmsg": "ok"
}
```

### 5. 获取用户信息

**接口地址：**
```
https://api.weixin.qq.com/sns/userinfo
```

**请求方式：** GET

**参数说明：**

| 参数 | 必须 | 说明 |
|------|------|------|
| access_token | 是 | 调用凭证 |
| openid | 是 | 用户标识 |
| lang | 否 | 语言版本，zh_CN/zh_TW/en，默认en |

**成功返回：**
```json
{
    "openid": "OPENID",
    "nickname": "NICKNAME",
    "sex": 1,
    "province": "PROVINCE",
    "city": "CITY",
    "country": "COUNTRY",
    "headimgurl": "https://thirdwx.qlogo.cn/mmopen/...",
    "privilege": ["PRIVILEGE1", "PRIVILEGE2"],
    "unionid": "UNIONID"
}
```

**字段说明：**

| 字段 | 说明 |
|------|------|
| openid | 用户标识，对当前开发者账号唯一 |
| nickname | 用户昵称 |
| sex | 用户性别，1为男性，2为女性 |
| province | 用户个人资料填写的省份 |
| city | 用户个人资料填写的城市 |
| country | 国家，如中国为CN |
| headimgurl | 用户头像URL |
| privilege | 用户特权信息 |
| unionid | 用户统一标识 |

## 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 40001 | invalid credential | 不合法的调用凭证 |
| 40002 | invalid grant_type | 不合法的grant_type |
| 40003 | invalid openid | 不合法的OpenID |
| 40029 | invalid code | 不合法的code |
| 40030 | invalid refresh_token | 不合法的refresh_token |

## 接口调用频率限制

| 接口名 | 频率限制 |
|--------|----------|
| 通过code换取access_token | 1万/分钟 |
| 刷新access_token | 5万/分钟 |
| 获取用户基本信息 | 5万/分钟 |

## 安全要求和注意事项

### 1. AppSecret安全
- **严禁**将AppSecret存储在客户端
- 建议将AppSecret存储在服务器端，通过服务器中转API调用
- 定期更换AppSecret

### 2. Access Token安全
- access_token有效期为2小时
- 不要在客户端长期存储access_token
- 使用refresh_token及时刷新access_token

### 3. 防CSRF攻击
- 使用state参数防止跨站请求伪造攻击
- state参数建议使用随机数加session进行校验

### 4. HTTPS要求
- 生产环境必须使用HTTPS
- redirect_uri必须使用HTTPS协议

### 5. 其他注意事项
- code有效期为10分钟，且只能使用一次
- refresh_token有效期为30天，无法续期
- 用户头像URL可能失效，建议保存头像图片到本地
- 根据法规要求，接口不再返回用户性别及地区信息（2021年10月20日起）

## 完整实现示例

### 前端实现（跳转方式）

```javascript
// 生成微信登录URL
function generateWeChatLoginUrl() {
    const appid = 'YOUR_APPID';
    const redirectUri = encodeURIComponent('https://yoursite.com/callback');
    const state = Math.random().toString(36).substring(2);
    
    // 保存state到sessionStorage用于验证
    sessionStorage.setItem('wechat_state', state);
    
    const url = `https://open.weixin.qq.com/connect/qrconnect?appid=${appid}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_login&state=${state}#wechat_redirect`;
    
    window.location.href = url;
}

// 处理回调
function handleCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const savedState = sessionStorage.getItem('wechat_state');
    
    if (state !== savedState) {
        console.error('State验证失败');
        return;
    }
    
    if (code) {
        // 发送code到后端换取用户信息
        fetch('/api/wechat/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ code })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 登录成功，处理用户信息
                console.log('用户信息:', data.userInfo);
            }
        });
    }
}
```

### 后端实现（Node.js示例）

```javascript
const express = require('express');
const axios = require('axios');
const app = express();

const APPID = 'YOUR_APPID';
const APPSECRET = 'YOUR_APPSECRET';

app.post('/api/wechat/login', async (req, res) => {
    const { code } = req.body;
    
    try {
        // 1. 通过code换取access_token
        const tokenResponse = await axios.get('https://api.weixin.qq.com/sns/oauth2/access_token', {
            params: {
                appid: APPID,
                secret: APPSECRET,
                code: code,
                grant_type: 'authorization_code'
            }
        });
        
        const { access_token, openid, unionid } = tokenResponse.data;
        
        if (!access_token) {
            return res.json({ success: false, error: '获取access_token失败' });
        }
        
        // 2. 获取用户信息
        const userResponse = await axios.get('https://api.weixin.qq.com/sns/userinfo', {
            params: {
                access_token: access_token,
                openid: openid,
                lang: 'zh_CN'
            }
        });
        
        const userInfo = userResponse.data;
        
        // 3. 处理用户信息（保存到数据库等）
        // ...
        
        res.json({
            success: true,
            userInfo: {
                openid: userInfo.openid,
                unionid: userInfo.unionid,
                nickname: userInfo.nickname,
                headimgurl: userInfo.headimgurl
            }
        });
        
    } catch (error) {
        console.error('微信登录错误:', error);
        res.json({ success: false, error: '登录失败' });
    }
});
```

## 官方参考资料

- [微信开放平台](https://open.weixin.qq.com/)
- [网站应用微信登录开发指南](https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html)
- [授权后接口调用指南](https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Authorized_Interface_Calling_UnionID.html)
- [微信开放平台开发者社区](https://developers.weixin.qq.com/community/)

---

**文档版本：** v1.0  
**最后更新：** 2025年1月  
**署名：** NEO JOU
