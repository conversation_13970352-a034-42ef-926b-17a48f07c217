import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { getUserIdentifier } from "@/lib/auth"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const tutorialId = parseInt(params.id)
    const userIdentifier = getUserIdentifier(request)

    if (!tutorialId) {
      return NextResponse.json({ error: "Invalid tutorial ID" }, { status: 400 })
    }

    // Get tutorial details using Supabase
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select(`
        id,
        title,
        description,
        content,
        price,
        tags,
        status,
        created_at,
        categories!category_id(name)
      `)
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      console.error("Tutorial not found:", tutorialError)
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 })
    }

    // Check if user has unlocked this tutorial
    const { data: unlock, error: unlockError } = await supabaseAdmin
      .from('user_unlocks')
      .select('id')
      .eq('user_identifier', userIdentifier)
      .eq('tutorial_id', tutorialId)
      .single()

    if (unlockError && unlockError.code !== 'PGRST116') {
      console.error("Error checking unlock status:", unlockError)
    }

    const isUnlocked = !unlockError && unlock

    // 检查教程状态和访问权限
    if (tutorial.status === 'draft') {
      // 草稿状态的教程任何人都不能访问
      return NextResponse.json({ error: "Tutorial not available" }, { status: 404 })
    } else if (tutorial.status === 'archived' && !isUnlocked) {
      // 下架的教程只有已解锁用户可以访问
      return NextResponse.json({ error: "Tutorial not available" }, { status: 404 })
    } else if (tutorial.status !== 'published' && tutorial.status !== 'archived') {
      // 其他未知状态不允许访问
      return NextResponse.json({ error: "Tutorial not available" }, { status: 404 })
    }

    // Format tutorial data
    const formattedTutorial = {
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      content: tutorial.content,
      price: tutorial.price,
      tags: tutorial.tags || [],
      status: tutorial.status,
      created_at: tutorial.created_at,
      category_name: tutorial.categories?.name || '未分类'
    }

    // If not unlocked, don't return the content
    if (!isUnlocked) {
      const { content, ...tutorialWithoutContent } = formattedTutorial
      return NextResponse.json({
        tutorial: tutorialWithoutContent,
        is_unlocked: false,
      })
    }

    return NextResponse.json({
      tutorial: formattedTutorial,
      is_unlocked: true,
    })
  } catch (error) {
    console.error("Get tutorial error:", error)
    return NextResponse.json({ error: "Failed to fetch tutorial" }, { status: 500 })
  }
}
