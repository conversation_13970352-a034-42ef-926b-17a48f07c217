import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const tutorialId = parseInt(params.id)
    
    if (!tutorialId) {
      return NextResponse.json({ error: "无效的教程ID" }, { status: 400 })
    }

    // 检查教程是否存在
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title, status')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "教程不存在" }, { status: 404 })
    }

    // 检查当前状态
    if (tutorial.status === 'archived') {
      return NextResponse.json({ error: "教程已经是下架状态" }, { status: 400 })
    }

    // 下架教程：设置状态为 archived，保留所有数据
    const { data: updatedTutorial, error: updateError } = await supabaseAdmin
      .from('tutorials')
      .update({ 
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('id', tutorialId)
      .select()
      .single()

    if (updateError) {
      console.error("下架教程错误:", updateError)
      return NextResponse.json({ error: "下架操作失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `教程"${tutorial.title}"已成功下架`,
      data: {
        tutorial_id: tutorialId,
        tutorial_title: tutorial.title,
        old_status: tutorial.status,
        new_status: 'archived'
      }
    })

  } catch (error) {
    console.error("下架教程异常:", error)
    return NextResponse.json({ 
      error: "服务器内部错误" 
    }, { status: 500 })
  }
}