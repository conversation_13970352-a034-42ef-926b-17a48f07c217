-- 更新UI/UX设计系统构建指南教程内容
-- 包含完整的进度跟踪标记

UPDATE tutorials 
SET content = '
<div class="tutorial-content">
    
    <!-- 教程介绍 -->
    <header data-section="intro" data-section-title="课程介绍" data-estimated-time="5">
        <h1>🎨 UI/UX 设计系统构建指南</h1>
        
        <div class="learning-objective" data-learning-objective="primary">
            <h3>🎯 课程学习目标</h3>
            <ul>
                <li>理解设计系统的基本概念和重要性</li>
                <li>掌握设计令牌(Design Tokens)的创建和使用</li>
                <li>学会构建可复用的组件库</li>
                <li>了解设计系统的维护和扩展策略</li>
                <li>掌握团队协作和设计交付流程</li>
            </ul>
        </div>

        <div class="progress-marker">
            <p><strong>📊 课程结构：</strong> 本课程包含6个主要章节，预计学习时间120分钟</p>
            <p><strong>🛠️ 使用工具：</strong> Figma, React, Storybook, Styled Components</p>
            <p><strong>📈 难度级别：</strong> 中级（需要基础的设计和前端开发知识）</p>
        </div>
    </header>

    <!-- 第一章：设计系统基础 -->
    <section data-section="1" data-section-title="设计系统基础" data-estimated-time="20">
        <h2>第一章：设计系统基础</h2>
        
        <h3>1.1 什么是设计系统？</h3>
        <p>设计系统是一套<span style="background: #fef3c7; padding: 2px 4px; border-radius: 3px;">完整的设计标准、组件库和指导原则</span>，用于在整个产品生态系统中创建一致的用户体验。它不仅仅是一个组件库，而是一个活的、可演化的系统。</p>

        <div style="background: #ecfdf5; border-left: 4px solid #10b981; padding: 1rem; margin: 1rem 0;">
            <strong>💡 专家提示：</strong> 设计系统的核心价值在于提高设计和开发效率，确保用户体验的一致性。
        </div>

        <h3>1.2 设计系统的核心组成</h3>
        <ul>
            <li><strong>设计令牌(Design Tokens)</strong> - 设计决策的最小单位</li>
            <li><strong>组件库</strong> - 可复用的UI组件</li>
            <li><strong>设计原则</strong> - 指导设计决策的准则</li>
            <li><strong>使用指南</strong> - 如何正确使用组件的文档</li>
            <li><strong>品牌规范</strong> - 视觉身份和语调指南</li>
        </ul>

        <div data-checkpoint="1-1" data-checkpoint-type="knowledge" data-points="15" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 1.1：基础概念理解</h4>
            <p>请确保你已经理解了设计系统的定义和核心组成部分。设计系统与组件库的主要区别是什么？</p>
            <details>
                <summary>点击查看答案</summary>
                <p>设计系统是一个更广泛的概念，包含设计原则、指南和流程，而组件库只是其中的一部分。</p>
            </details>
        </div>

        <h3>1.3 著名设计系统案例分析</h3>
        <div data-interactive="case-study" data-required="true" style="background: #dbeafe; border: 2px solid #3b82f6; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>📝 案例分析练习</h4>
            <p>研究以下知名设计系统，分析它们的特点：</p>
            <ul>
                <li><strong>Material Design (Google)</strong> - 基于纸质隐喻的设计语言</li>
                <li><strong>Human Interface Guidelines (Apple)</strong> - 注重简洁和用户体验</li>
                <li><strong>Ant Design</strong> - 企业级应用的设计语言</li>
                <li><strong>Atlassian Design System</strong> - 协作工具的设计规范</li>
            </ul>
            <p><em>选择其中一个系统，分析其设计原则和组件特点（花费5-10分钟）</em></p>
        </div>
    </section>

    <!-- 第二章：设计令牌系统 -->
    <section data-section="2" data-section-title="设计令牌系统" data-estimated-time="25">
        <h2>第二章：设计令牌(Design Tokens)系统</h2>

        <h3>2.1 设计令牌的概念</h3>
        <p>设计令牌是<span style="background: #fef3c7; padding: 2px 4px; border-radius: 3px;">设计决策的原子单位</span>，它们以代码友好的格式存储设计属性值。想象它们是设计系统的"基因"，决定了整个系统的视觉特征。</p>

        <h3>2.2 令牌的分类体系</h3>
        
        <h4>2.2.1 基础令牌(Primitive Tokens)</h4>
        <pre style="background: #1f2937; color: #f9fafb; padding: 1rem; border-radius: 6px; overflow-x: auto;">{
  "color": {
    "blue": {
      "50": "#eff6ff",
      "100": "#dbeafe", 
      "500": "#3b82f6",
      "900": "#1e3a8a"
    }
  },
  "spacing": {
    "xs": "4px",
    "sm": "8px", 
    "md": "16px",
    "lg": "24px"
  }
}</pre>

        <div data-checkpoint="2-1" data-checkpoint-type="practical" data-points="20" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 2.1：令牌系统设计</h4>
            <p>为一个电商网站设计基础的颜色令牌系统，包括：</p>
            <ul>
                <li>主品牌色(3个层级)</li>
                <li>辅助色系统</li>
                <li>语义颜色(成功、警告、错误)</li>
                <li>中性色阶(至少5个层级)</li>
            </ul>
        </div>
    </section>

    <!-- 第三章：色彩系统设计 -->
    <section data-section="3" data-section-title="色彩系统设计" data-estimated-time="20">
        <h2>第三章：色彩系统设计</h2>

        <h3>3.1 色彩理论基础</h3>
        <p>在设计系统中，色彩不仅仅是美学选择，更是<span style="background: #fef3c7; padding: 2px 4px; border-radius: 3px;">传达信息和引导用户行为</span>的重要工具。</p>

        <div style="background: #fef2f2; border-left: 4px solid #ef4444; padding: 1rem; margin: 1rem 0;">
            <strong>⚠️ 无障碍要求：</strong> 根据WCAG 2.1标准，正常文本需要4.5:1的对比度，大文本需要3:1的对比度。
        </div>

        <div data-checkpoint="3-1" data-checkpoint-type="practical" data-points="25" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 3.1：色彩系统构建</h4>
            <p>设计一个完整的色彩系统，确保所有文本色彩都通过WCAG AA标准。</p>
        </div>
    </section>

    <!-- 第四章：排版系统 -->
    <section data-section="4" data-section-title="排版系统" data-estimated-time="15">
        <h2>第四章：排版系统(Typography)</h2>

        <h3>4.1 字体层级体系</h3>
        <p>良好的排版系统应该建立清晰的<span style="background: #fef3c7; padding: 2px 4px; border-radius: 3px;">视觉层级</span>，帮助用户快速理解内容结构。</p>

        <div data-checkpoint="4-1" data-checkpoint-type="design" data-points="15" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 4.1：排版系统设计</h4>
            <p>创建一个排版系统，定义H1-H6标题层级和各种文本样式。</p>
        </div>
    </section>

    <!-- 第五章：组件库构建 -->
    <section data-section="5" data-section-title="组件库构建" data-estimated-time="30">
        <h2>第五章：组件库构建</h2>

        <h3>5.1 组件设计原则</h3>
        <p>设计系统中的组件应该遵循<strong>原子设计理论</strong>，从最小的原子组件构建复杂的界面。</p>

        <div data-interactive="component-design" data-required="true" style="background: #dbeafe; border: 2px solid #3b82f6; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎨 组件设计练习</h4>
            <p>设计一个完整的输入框组件，包含所有交互状态和尺寸变体。</p>
        </div>

        <div data-checkpoint="5-1" data-checkpoint-type="practical" data-points="30" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 5.1：组件库构建</h4>
            <p>构建Button、Input、Card、Badge四个基础组件的完整设计规范。</p>
        </div>
    </section>

    <!-- 第六章：工具和流程 -->
    <section data-section="6" data-section-title="工具和流程" data-estimated-time="20">
        <h2>第六章：工具和协作流程</h2>

        <h3>6.1 设计工具生态</h3>
        <p>建立从设计到开发的<span style="background: #fef3c7; padding: 2px 4px; border-radius: 3px;">自动化同步流程</span></p>

        <div data-checkpoint="6-1" data-checkpoint-type="planning" data-points="20" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 检查点 6.1：工具链搭建</h4>
            <p>规划一个完整的设计系统工具链和团队协作流程。</p>
        </div>
    </section>

    <!-- 课程总结 -->
    <section data-section="conclusion" data-section-title="课程总结" data-estimated-time="10">
        <h2>🎉 课程总结</h2>

        <div data-learning-objective="summary">
            <h3>🏆 你已经掌握的技能</h3>
            <ul>
                <li>✅ 理解设计系统的核心概念和价值</li>
                <li>✅ 掌握设计令牌的创建和管理</li>
                <li>✅ 能够构建可访问的色彩和排版系统</li>
                <li>✅ 学会组件化设计思维</li>
                <li>✅ 了解团队协作和工具链建设</li>
            </ul>
        </div>

        <div data-checkpoint="final" data-checkpoint-type="completion" data-points="50" style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
            <h4>🎯 最终检查点：课程完成</h4>
            <p>恭喜你完成了UI/UX设计系统构建指南！你已经具备了构建设计系统的基础能力。</p>
        </div>
    </section>

    <div style="display: flex; justify-content: space-between; margin: 2rem 0; padding: 1rem; background: #f1f5f9; border-radius: 8px;">
        <div><strong>📚 总计：</strong> 6个章节，7个检查点，3个互动练习</div>
        <div><strong>⏱️ 预计学习时间：</strong> 120分钟</div>
    </div>

</div>

<style>
    .tutorial-content { max-width: 800px; margin: 0 auto; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.6; }
    .tutorial-content h1 { color: #1f2937; margin-bottom: 1.5rem; }
    .tutorial-content h2 { color: #374151; margin: 2rem 0 1rem 0; padding-bottom: 0.5rem; border-bottom: 2px solid #e5e7eb; }
    .tutorial-content h3 { color: #4b5563; margin: 1.5rem 0 0.5rem 0; }
    .tutorial-content p { margin: 1rem 0; color: #6b7280; }
    .tutorial-content ul, .tutorial-content ol { margin: 1rem 0; padding-left: 1.5rem; }
    .tutorial-content li { margin: 0.5rem 0; color: #6b7280; }
    .tutorial-content strong { color: #374151; }
    .tutorial-content code { background: #f3f4f6; padding: 2px 6px; border-radius: 4px; font-family: Monaco, Menlo, monospace; font-size: 0.9em; }
    .tutorial-content pre { background: #1f2937; color: #f9fafb; padding: 1rem; border-radius: 6px; overflow-x: auto; margin: 1rem 0; }
    [data-section] { scroll-margin-top: 2rem; }
    [data-checkpoint] { cursor: pointer; transition: all 0.2s ease; }
    [data-checkpoint]:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
    [data-interactive] { cursor: pointer; }
    details { margin: 0.5rem 0; }
    summary { cursor: pointer; font-weight: 500; color: #3b82f6; }
    summary:hover { color: #2563eb; }
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // 进度跟踪功能
    const sections = document.querySelectorAll("[data-section]");
    const checkpoints = document.querySelectorAll("[data-checkpoint]");
    const interactives = document.querySelectorAll("[data-interactive]");
    
    let currentProgress = {
        currentSection: null,
        visitedSections: new Set(),
        completedCheckpoints: new Set(),
        interactedElements: new Set(),
        startTime: Date.now(),
        sectionTimes: {}
    };

    // 章节进度跟踪
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.getAttribute("data-section");
                const sectionTitle = entry.target.getAttribute("data-section-title");
                
                if (currentProgress.currentSection !== sectionId) {
                    currentProgress.currentSection = sectionId;
                    currentProgress.visitedSections.add(sectionId);
                    
                    if (!currentProgress.sectionTimes[sectionId]) {
                        currentProgress.sectionTimes[sectionId] = Date.now();
                    }
                    
                    console.log(`📖 正在学习: ${sectionTitle} (${sectionId})`);
                    updateProgress();
                }
            }
        });
    }, { threshold: 0.3 });

    sections.forEach(section => observer.observe(section));

    // 检查点互动跟踪
    checkpoints.forEach(checkpoint => {
        checkpoint.addEventListener("click", function() {
            const checkpointId = this.getAttribute("data-checkpoint");
            const points = parseInt(this.getAttribute("data-points")) || 10;
            
            if (!currentProgress.completedCheckpoints.has(checkpointId)) {
                currentProgress.completedCheckpoints.add(checkpointId);
                this.style.background = "#d1fae5";
                this.style.borderColor = "#10b981";
                
                // 添加完成标记
                const completeMark = document.createElement("span");
                completeMark.innerHTML = " ✅";
                completeMark.style.color = "#10b981";
                this.querySelector("h4").appendChild(completeMark);
                
                console.log(`🎯 检查点完成: ${checkpointId} (+${points}点)`);
                updateProgress();
            }
        });
    });

    // 互动元素跟踪
    interactives.forEach(interactive => {
        interactive.addEventListener("click", function() {
            const interactiveId = this.getAttribute("data-interactive");
            if (!currentProgress.interactedElements.has(interactiveId)) {
                currentProgress.interactedElements.add(interactiveId);
                this.style.background = "#ecfdf5";
                this.style.borderColor = "#10b981";
                
                console.log(`💡 互动元素激活: ${interactiveId}`);
                updateProgress();
            }
        });
    });

    // 更新进度到后端
    function updateProgress() {
        const totalSections = sections.length;
        const visitedSections = currentProgress.visitedSections.size;
        const totalCheckpoints = checkpoints.length;
        const completedCheckpoints = currentProgress.completedCheckpoints.size;
        
        const progressPercentage = Math.round(
            ((visitedSections / totalSections) * 0.6 + 
             (completedCheckpoints / totalCheckpoints) * 0.4) * 100
        );
        
        const currentTime = Date.now();
        const timeSpent = Math.round((currentTime - currentProgress.startTime) / 1000);
        
        let status = "not_started";
        if (progressPercentage >= 100) {
            status = "completed";
        } else if (progressPercentage > 0) {
            status = "in_progress";
        }
        
        // 发送到学习进度API
        const progressData = {
            tutorialId: 2, // UI/UX设计系统教程ID
            status: status,
            progressPercentage: progressPercentage,
            timeSpent: timeSpent,
            interactionData: {
                visitedSections: Array.from(currentProgress.visitedSections),
                completedCheckpoints: Array.from(currentProgress.completedCheckpoints),
                interactedElements: Array.from(currentProgress.interactedElements),
                sectionTimes: currentProgress.sectionTimes,
                deviceType: "web",
                scrollPercentage: Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100),
                interactionCount: currentProgress.completedCheckpoints.size + currentProgress.interactedElements.size
            }
        };
        
        // 实际发送到后端（每30秒或重要事件时）
        fetch("/api/learning/progress", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(progressData)
        }).catch(err => console.warn("Progress update failed:", err));
        
        console.log(`📊 学习进度: ${progressPercentage}%, 用时: ${Math.round(timeSpent/60)}分钟`);
    }

    // 定期保存进度
    setInterval(updateProgress, 30000); // 每30秒保存一次

    // 页面离开时保存进度
    window.addEventListener("beforeunload", updateProgress);
});
</script>',
description = '学习如何构建完整的设计系统，包含组件库、设计规范、品牌指导等。从设计到开发的完整流程。包含6个章节、7个检查点和3个互动练习，预计学习时间120分钟。'
WHERE title = 'UI/UX 设计系统构建指南';