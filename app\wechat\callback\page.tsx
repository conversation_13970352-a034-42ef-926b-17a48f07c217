"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle, AlertCircle, Loader2, MessageCircle } from "lucide-react"

export default function WeChatCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [userInfo, setUserInfo] = useState<any>(null)

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code')
      const state = searchParams.get('state')
      const error = searchParams.get('error')
      const errorDescription = searchParams.get('error_description')

      // 检查是否有错误参数
      if (error) {
        setStatus('error')
        setMessage(errorDescription || '微信授权被取消或失败')
        return
      }

      // 检查是否有code
      if (!code) {
        setStatus('error')
        setMessage('未获取到授权码，请重新尝试登录')
        return
      }

      try {
        // 调用后端API处理登录
        const response = await fetch('/api/wechat/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code, state }),
        })

        const data = await response.json()

        if (data.success) {
          setStatus('success')
          setUserInfo(data.userInfo)
          setMessage(data.isDemo ? '演示登录成功！' : '微信登录成功！')
          
          // 将用户信息传递给父窗口（如果是弹窗）或保存到localStorage
          if (window.opener) {
            window.opener.postMessage({
              type: 'WECHAT_LOGIN_SUCCESS',
              userInfo: data.userInfo,
              isDemo: data.isDemo
            }, window.location.origin)
            window.close()
          } else {
            // 保存用户信息到localStorage或sessionStorage
            localStorage.setItem('userInfo', JSON.stringify(data.userInfo))
            
            // 3秒后跳转到主页
            setTimeout(() => {
              router.push('/')
            }, 3000)
          }
        } else {
          setStatus('error')
          setMessage(data.error || '登录失败，请重试')
        }
      } catch (error) {
        console.error('登录处理错误:', error)
        setStatus('error')
        setMessage('网络错误，请检查连接后重试')
      }
    }

    handleCallback()
  }, [searchParams, router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {status === 'loading' && (
              <Loader2 className="h-12 w-12 text-green-600 animate-spin" />
            )}
            {status === 'success' && (
              <CheckCircle className="h-12 w-12 text-green-600" />
            )}
            {status === 'error' && (
              <AlertCircle className="h-12 w-12 text-red-600" />
            )}
          </div>
          
          <CardTitle className="flex items-center justify-center">
            <MessageCircle className="h-5 w-5 mr-2 text-green-600" />
            微信登录
          </CardTitle>
          
          <CardDescription>
            {status === 'loading' && '正在处理微信登录...'}
            {status === 'success' && '登录成功！'}
            {status === 'error' && '登录失败'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          <p className={`text-sm ${
            status === 'success' ? 'text-green-700' : 
            status === 'error' ? 'text-red-700' : 
            'text-gray-600'
          }`}>
            {message}
          </p>

          {userInfo && status === 'success' && (
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center space-x-3">
                <img 
                  src={userInfo.headimgurl || '/placeholder.svg'} 
                  alt={userInfo.nickname}
                  className="w-10 h-10 rounded-full"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg'
                  }}
                />
                <div className="text-left">
                  <p className="font-medium text-green-800">{userInfo.nickname}</p>
                  <p className="text-xs text-green-600">
                    {userInfo.province && userInfo.city ? 
                      `${userInfo.province} ${userInfo.city}` : 
                      '微信用户'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {status === 'success' && !window.opener && (
            <p className="text-xs text-gray-500">
              页面将在3秒后自动跳转到首页...
            </p>
          )}

          {status === 'error' && (
            <div className="space-y-2">
              <Button 
                onClick={() => router.push('/')} 
                className="w-full"
                variant="outline"
              >
                返回首页
              </Button>
              <Button 
                onClick={() => window.location.reload()} 
                className="w-full"
                variant="default"
              >
                重新尝试
              </Button>
            </div>
          )}

          {status === 'loading' && (
            <div className="space-y-2">
              <div className="animate-pulse h-2 bg-gray-200 rounded"></div>
              <div className="animate-pulse h-2 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}