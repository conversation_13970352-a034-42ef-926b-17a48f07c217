#!/bin/bash

# Setup local git hook for documentation refresh
# Run this once: chmod +x .github/setup-hooks.sh && ./.github/setup-hooks.sh

cat > .git/hooks/post-merge << 'EOF'
#!/bin/bash

# Only run on main branch merges
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$BRANCH" = "main" ]; then
    echo "🔄 Refreshing documentation after merge..."
    
    # Check if Claude Code is available
    if command -v claude &> /dev/null; then
        claude /refresh-docs
        
        # Check if docs were updated
        if ! git diff --quiet .claude/steering/ specs/; then
            echo "📝 Documentation updated, creating commit..."
            git add .claude/steering/ specs/
            git commit -m "docs: auto-refresh documentation after merge

🤖 Generated with Claude Code

Co-Authored-By: <PERSON> <<EMAIL>>"
            echo "✅ Documentation refresh complete"
        else
            echo "📋 Documentation already up to date"
        fi
    else
        echo "⚠️  Claude Code not found, skipping documentation refresh"
    fi
fi
EOF

chmod +x .git/hooks/post-merge
echo "✅ Git hook installed! Documentation will refresh after merges to main."