name: Refresh Documentation

on:
  pull_request:
    types: [closed]
    branches: [main]

jobs:
  refresh-docs:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 10  # Get recent history for analysis
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Claude Code CLI
      run: |
        curl -fsSL https://claude.ai/install.sh | bash
        echo "$HOME/.local/bin" >> $GITHUB_PATH
    
    - name: Configure Claude Code
      run: |
        claude_code_oauth_token: ${{ secrets.CLAUDE_CODE_OAUTH_TOKEN }}
      
    - name: Run documentation refresh
      run: claude /refresh-docs
      
    - name: Check for documentation changes
      id: check_changes
      run: |
        git add -A
        if git diff --cached --quiet; then
          echo "changes=false" >> $GITHUB_OUTPUT
        else
          echo "changes=true" >> $GITHUB_OUTPUT
        fi
    
    - name: Commit and push documentation updates
      if: steps.check_changes.outputs.changes == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git commit -m "docs: auto-refresh documentation after PR merge
        
        🤖 Generated with Claude Code
        
        Co-Authored-By: Claude <<EMAIL>>"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}