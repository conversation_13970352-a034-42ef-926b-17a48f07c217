#!/usr/bin/env node

/**
 * 在Supabase中执行内容管理系统数据库扩展
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function executeContentManagementSetup() {
  try {
    console.log('🚀 开始执行内容管理系统数据库扩展...\n');

    // 创建内容版本表
    console.log('📝 创建 tutorial_versions 表...');
    const { error: versionsError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tutorial_versions (
          id SERIAL PRIMARY KEY,
          tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
          version_number INTEGER NOT NULL,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          content TEXT NOT NULL,
          meta_description VARCHAR(160),
          meta_keywords TEXT[],
          status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
          author_id VARCHAR(100),
          editor_id VARCHAR(100),
          change_summary TEXT,
          word_count INTEGER DEFAULT 0,
          reading_time INTEGER DEFAULT 0,
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(tutorial_id, version_number)
        );
      `
    });

    if (versionsError) {
      console.error('❌ 创建版本表失败:', versionsError);
    } else {
      console.log('✅ tutorial_versions 表创建成功');
    }

    // 创建媒体资源表
    console.log('📁 创建 tutorial_media 表...');
    const { error: mediaError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tutorial_media (
          id SERIAL PRIMARY KEY,
          tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
          file_name VARCHAR(255) NOT NULL,
          original_name VARCHAR(255) NOT NULL,
          file_type VARCHAR(50) NOT NULL,
          mime_type VARCHAR(100) NOT NULL,
          file_size BIGINT NOT NULL,
          width INTEGER,
          height INTEGER,
          duration INTEGER,
          storage_path TEXT NOT NULL,
          public_url TEXT NOT NULL,
          thumbnail_url TEXT,
          alt_text VARCHAR(255),
          caption TEXT,
          usage_count INTEGER DEFAULT 0,
          uploaded_by VARCHAR(100),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `
    });

    if (mediaError) {
      console.error('❌ 创建媒体表失败:', mediaError);
    } else {
      console.log('✅ tutorial_media 表创建成功');
    }

    // 创建内容模板表
    console.log('📋 创建 tutorial_templates 表...');
    const { error: templatesError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tutorial_templates (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          template_content TEXT NOT NULL,
          category VARCHAR(50),
          tags TEXT[],
          preview_image TEXT,
          usage_count INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_by VARCHAR(100),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `
    });

    if (templatesError) {
      console.error('❌ 创建模板表失败:', templatesError);
    } else {
      console.log('✅ tutorial_templates 表创建成功');
    }

    // 创建SEO表
    console.log('🔍 创建 tutorial_seo 表...');
    const { error: seoError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tutorial_seo (
          id SERIAL PRIMARY KEY,
          tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
          slug VARCHAR(255) UNIQUE NOT NULL,
          canonical_url TEXT,
          og_title VARCHAR(60),
          og_description VARCHAR(160),
          og_image TEXT,
          og_type VARCHAR(20) DEFAULT 'article',
          twitter_card VARCHAR(20) DEFAULT 'summary_large_image',
          structured_data JSONB,
          meta_robots VARCHAR(50) DEFAULT 'index,follow',
          sitemap_priority DECIMAL(2,1) DEFAULT 0.5,
          sitemap_frequency VARCHAR(20) DEFAULT 'weekly',
          focus_keyword VARCHAR(100),
          readability_score INTEGER,
          seo_score INTEGER DEFAULT 0,
          last_analyzed TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `
    });

    if (seoError) {
      console.error('❌ 创建SEO表失败:', seoError);
    } else {
      console.log('✅ tutorial_seo 表创建成功');
    }

    // 创建统计表
    console.log('📊 创建 tutorial_analytics 表...');
    const { error: analyticsError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tutorial_analytics (
          id SERIAL PRIMARY KEY,
          tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
          date DATE NOT NULL,
          view_count INTEGER DEFAULT 0,
          unique_visitors INTEGER DEFAULT 0,
          avg_reading_time INTEGER DEFAULT 0,
          bounce_rate DECIMAL(5,2) DEFAULT 0,
          completion_rate DECIMAL(5,2) DEFAULT 0,
          share_count INTEGER DEFAULT 0,
          bookmark_count INTEGER DEFAULT 0,
          comment_count INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(tutorial_id, date)
        );
      `
    });

    if (analyticsError) {
      console.error('❌ 创建统计表失败:', analyticsError);
    } else {
      console.log('✅ tutorial_analytics 表创建成功');
    }

    // 创建索引
    console.log('⚡ 创建性能索引...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_tutorial_versions_tutorial_id ON tutorial_versions(tutorial_id);',
      'CREATE INDEX IF NOT EXISTS idx_tutorial_versions_status ON tutorial_versions(status);',
      'CREATE INDEX IF NOT EXISTS idx_tutorial_media_tutorial_id ON tutorial_media(tutorial_id);',
      'CREATE INDEX IF NOT EXISTS idx_tutorial_media_type ON tutorial_media(file_type);',
      'CREATE INDEX IF NOT EXISTS idx_tutorial_seo_slug ON tutorial_seo(slug);',
      'CREATE INDEX IF NOT EXISTS idx_tutorial_analytics_tutorial_date ON tutorial_analytics(tutorial_id, date);'
    ];

    for (const indexSql of indexes) {
      const { error } = await supabaseAdmin.rpc('exec_sql', { sql: indexSql });
      if (error) {
        console.warn('⚠️ 索引创建警告:', error.message);
      }
    }

    console.log('✅ 索引创建完成');

    // 验证表创建
    console.log('\n🔍 验证表结构...');
    const { data: tables, error: tableError } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['tutorial_versions', 'tutorial_media', 'tutorial_templates', 'tutorial_seo', 'tutorial_analytics']);

    if (tableError) {
      console.error('❌ 表验证失败:', tableError);
    } else {
      console.log('📋 已创建的表:');
      tables?.forEach(table => {
        console.log(`   ✓ ${table.table_name}`);
      });
    }

    console.log('\n🎉 内容管理系统数据库扩展完成！');
    console.log('\n💡 接下来可以：');
    console.log('1. 测试媒体文件上传功能');
    console.log('2. 创建内容版本管理界面');
    console.log('3. 实现SEO优化功能');

  } catch (error) {
    console.error('❌ 数据库扩展失败:', error);
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  executeContentManagementSetup();
}

module.exports = { executeContentManagementSetup };