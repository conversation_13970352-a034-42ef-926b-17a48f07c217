"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { QrCode, MessageCircle, ExternalLink, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface WechatLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess: (userInfo: any) => void
}

export function WechatLoginDialog({ open, onOpenChange, onLoginSuccess }: WechatLoginDialogProps) {
  const [loading, setLoading] = useState(false)
  const [authUrl, setAuthUrl] = useState("")
  const [qrUrl, setQrUrl] = useState("")
  const [isDemo, setIsDemo] = useState(false)
  const { toast } = useToast()

  // 获取微信授权URL
  const getWeChatAuthUrl = async () => {
    try {
      const response = await fetch('/api/wechat/auth', {
        method: 'GET',
      })
      
      const data = await response.json()
      
      if (data.success) {
        setAuthUrl(data.authUrl)
        setQrUrl(data.qrUrl)
        setIsDemo(data.isDemo)
      } else {
        toast({
          title: "获取授权链接失败",
          description: data.error || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('获取微信授权URL失败:', error)
      toast({
        title: "网络错误",
        description: "请检查网络连接后重试",
        variant: "destructive",
      })
    }
  }

  // 组件打开时获取授权URL
  useEffect(() => {
    if (open) {
      getWeChatAuthUrl()
    }
  }, [open])

  // 监听来自回调页面的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return
      
      if (event.data.type === 'WECHAT_LOGIN_SUCCESS') {
        onLoginSuccess(event.data.userInfo)
        onOpenChange(false)
        toast({
          title: "登录成功",
          description: `欢迎 ${event.data.userInfo.nickname}！${event.data.isDemo ? ' (演示模式)' : ''}`,
        })
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [onLoginSuccess, onOpenChange, toast])

  // 处理二维码登录
  const handleQRLogin = () => {
    if (isDemo) {
      // 演示模式：模拟登录
      setLoading(true)
      setTimeout(() => {
        const mockUserInfo = {
          id: "wx_" + Math.random().toString(36).substr(2, 9),
          nickname: "微信用户" + Math.floor(Math.random() * 1000),
          avatar: "/placeholder.svg?height=40&width=40",
          openid: "demo_openid_" + Math.random().toString(36).substr(2, 16),
        }
        onLoginSuccess(mockUserInfo)
        onOpenChange(false)
        setLoading(false)
        toast({
          title: "登录成功 (演示模式)",
          description: `欢迎 ${mockUserInfo.nickname}！`,
        })
      }, 2000)
    } else {
      // 生产模式：打开微信授权页面
      if (authUrl && authUrl !== "#") {
        const popup = window.open(
          authUrl,
          'wechat_login',
          'width=500,height=600,scrollbars=yes,resizable=yes,status=yes'
        )
        
        if (!popup) {
          toast({
            title: "弹窗被阻止",
            description: "请允许弹窗后重试，或直接点击授权链接",
            variant: "destructive",
          })
        }
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MessageCircle className="h-5 w-5 mr-2 text-green-600" />
            微信扫码登录
          </DialogTitle>
          <DialogDescription>
            使用微信扫码快速登录，安全便捷
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 二维码登录区域 */}
          <div className="text-center space-y-4">
            <div className="mx-auto w-48 h-48 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              {loading ? (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">
                    {isDemo ? "模拟登录中..." : "等待扫码..."}
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <QrCode className="h-16 w-16 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600">微信扫码登录</p>
                  {isDemo && (
                    <p className="text-xs text-orange-600 mt-1">演示模式</p>
                  )}
                </div>
              )}
            </div>

            <div className="space-y-3">
              {isDemo ? (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3">
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 text-orange-600 mr-2" />
                    <p className="text-sm text-orange-700">
                      演示模式：未配置微信AppID，点击按钮模拟登录
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-600">请使用微信扫描二维码登录</p>
              )}

              <Button 
                onClick={handleQRLogin} 
                disabled={loading} 
                className="w-full"
              >
                {loading ? 
                  (isDemo ? "模拟登录中..." : "等待扫码中...") : 
                  (isDemo ? "点击模拟登录" : "开始扫码登录")
                }
              </Button>

              {!isDemo && authUrl && authUrl !== "#" && (
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={() => window.open(authUrl, '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  在新窗口中打开
                </Button>
              )}
            </div>
          </div>

          {/* 登录说明 */}
          <div className="text-xs text-gray-500 space-y-1 border-t pt-4">
            <p>• 登录即表示同意《用户协议》和《隐私政策》</p>
            <p>• 首次登录将自动创建账户</p>
            <p>• 登录后可享受个性化服务</p>
            {isDemo && (
              <p className="text-orange-600">• 当前为演示模式，请配置微信AppID启用真实登录</p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
