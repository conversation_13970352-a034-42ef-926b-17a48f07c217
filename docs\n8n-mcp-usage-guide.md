# n8n-MCP 使用指南

## 📋 目录

1. [项目概述](#项目概述)
2. [核心功能](#核心功能)
3. [快速开始](#快速开始)
4. [工具详解](#工具详解)
5. [使用场景](#使用场景)
6. [最佳实践](#最佳实践)
7. [故障排查](#故障排查)
8. [进阶功能](#进阶功能)

## 📖 项目概述

n8n-MCP (Model Context Protocol) 是一个专为AI助手设计的n8n工作流自动化平台接口。它为AI模型提供了对n8n生态系统的全面访问能力，包括532个节点的文档、配置验证、工作流创建等功能。

### 🎯 核心价值

- **全面覆盖**：支持532个n8n节点，88%文档覆盖率
- **智能验证**：自动验证节点配置，减少部署错误
- **AI友好**：专为AI助手优化的接口设计
- **高性能**：平均响应时间12ms，数据库仅15MB
- **易集成**：支持多种部署方式和MCP客户端

### 📊 统计数据

- **节点总数**：532个
- **AI工具节点**：267个（50.2%）
- **触发器节点**：108个
- **文档覆盖率**：88%（470/532）
- **支持包**：n8n-nodes-base (436个) + @n8n/n8n-nodes-langchain (96个)

## 🚀 核心功能

### 1. 节点发现与搜索
- 全文搜索所有节点
- 按类别、包、类型筛选
- AI工具节点专项列表

### 2. 配置管理
- 节点属性查询
- 配置验证与修复建议
- 依赖关系分析

### 3. 工作流管理
- 工作流创建与验证
- 模板库访问
- 连接检查与表达式验证

### 4. AI集成
- 任何节点都可作为AI工具
- 预配置任务模板
- 智能配置建议

## ⚡ 快速开始

### 基础工作流程

```javascript
// 1. 搜索需要的节点
search_nodes({query: "email", mode: "OR"})

// 2. 获取节点基本信息
get_node_essentials("nodes-base.emailSend")

// 3. 验证配置
validate_node_operation("nodes-base.emailSend", {
  fromEmail: "<EMAIL>",
  toEmail: "<EMAIL>",
  subject: "Test Email",
  message: "Hello World"
})

// 4. 创建工作流
validate_workflow({
  nodes: [...],
  connections: {...}
})
```

### 常用命令速查

```bash
# 节点发现
list_nodes({limit: 50, category: "trigger"})
search_nodes({query: "slack webhook", mode: "AND"})
list_ai_tools()

# 配置获取
get_node_essentials("nodes-base.httpRequest")
search_node_properties("nodes-base.slack", "auth")

# 验证工具
validate_node_minimal("nodes-base.webhook", config)
validate_workflow_connections(workflow)

# 模板工具
list_tasks()
get_node_for_task("send_email")
```

## 🛠️ 工具详解

### 发现工具 (Discovery Tools)

#### `search_nodes`
**功能**：全文搜索节点
**参数**：
- `query`: 搜索关键词
- `mode`: "OR"(任意匹配) | "AND"(全部匹配) | "FUZZY"(模糊匹配)
- `limit`: 结果数量限制

**示例**：
```javascript
// 搜索邮件相关节点
search_nodes({
  query: "email send",
  mode: "AND",
  limit: 10
})

// 模糊搜索（容错）
search_nodes({
  query: "webhok",  // 拼写错误
  mode: "FUZZY"
})
```

#### `list_nodes`
**功能**：按条件列出节点
**参数**：
- `category`: "trigger" | "transform" | "output" | "input" | "AI"
- `package`: "n8n-nodes-base" | "@n8n/n8n-nodes-langchain"
- `isAITool`: 是否为AI工具
- `limit`: 结果数量

**示例**：
```javascript
// 列出所有触发器节点
list_nodes({
  category: "trigger",
  limit: 50
})

// 列出AI相关节点
list_nodes({
  package: "@n8n/n8n-nodes-langchain",
  isAITool: true
})
```

### 配置工具 (Configuration Tools)

#### `get_node_essentials`
**功能**：获取节点核心属性（推荐首选）
**特点**：
- 返回10-20个关键属性
- 包含使用示例
- 响应快速（<5KB）

**示例**：
```javascript
get_node_essentials("nodes-base.httpRequest")
```

#### `get_node_info`
**功能**：获取完整节点架构
**特点**：
- 返回所有属性和操作
- 详细的类型定义
- 较大响应（100KB+）

**使用建议**：仅在需要完整信息时使用

### 验证工具 (Validation Tools)

#### `validate_node_operation`
**功能**：全面验证节点配置
**参数**：
- `nodeType`: 节点类型
- `config`: 配置对象
- `profile`: "minimal" | "runtime" | "ai-friendly" | "strict"

**示例**：
```javascript
validate_node_operation("nodes-base.slack", {
  resource: "message",
  operation: "post",
  channel: "#general",
  text: "Hello World"
}, {profile: "ai-friendly"})
```

#### `validate_workflow`
**功能**：验证完整工作流
**检查项目**：
- 节点配置正确性
- 连接有效性
- 表达式语法
- AI工具链接

## 📝 使用场景

### 场景1：创建简单的邮件通知工作流

```javascript
// 1. 搜索邮件节点
const emailNodes = await search_nodes({query: "email send"});

// 2. 获取配置信息
const emailConfig = await get_node_essentials("nodes-base.emailSend");

// 3. 创建工作流
const workflow = {
  name: "邮件通知工作流",
  nodes: [
    {
      name: "手动触发器",
      type: "n8n-nodes-base.manualTrigger",
      position: [100, 200]
    },
    {
      name: "发送邮件",
      type: "n8n-nodes-base.emailSend",
      position: [300, 200],
      parameters: {
        fromEmail: "<EMAIL>",
        toEmail: "<EMAIL>",
        subject: "通知邮件",
        message: "这是一条自动发送的通知邮件"
      }
    }
  ],
  connections: {
    "手动触发器": {
      "main": [[{
        "node": "发送邮件",
        "type": "main",
        "index": 0
      }]]
    }
  }
};

// 4. 验证工作流
const validation = await validate_workflow(workflow);
```

### 场景2：AI驱动的数据处理工作流

```javascript
// 1. 获取AI工具列表
const aiTools = await list_ai_tools();

// 2. 获取预配置的AI代理
const aiAgent = await get_node_for_task("ai_agent_workflow");

// 3. 配置多工具AI代理
const agentConfig = {
  resource: "agent",
  operation: "execute",
  tools: [
    "nodes-base.googleSheets",
    "nodes-base.slack",
    "nodes-base.httpRequest"
  ],
  prompt: "分析Google表格中的数据，生成报告并发送到Slack"
};

// 4. 验证配置
const validation = await validate_node_operation(
  "nodes-langchain.agent", 
  agentConfig
);
```

### 场景3：API集成与错误处理

```javascript
// 1. 获取HTTP请求模板
const httpTemplate = await get_node_for_task("api_call_with_retry");

// 2. 配置带重试的API调用
const apiConfig = {
  method: "POST",
  url: "https://api.example.com/data",
  body: {
    data: "{{ $json.inputData }}"
  },
  options: {
    retry: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1000
    }
  }
};

// 3. 验证配置
const validation = await validate_node_minimal(
  "nodes-base.httpRequest", 
  apiConfig
);
```

## 💡 最佳实践

### 1. 节点选择策略

```javascript
// ✅ 推荐：先用essentials获取概览
const overview = await get_node_essentials("nodes-base.slack");

// ✅ 需要时再获取完整信息
if (needsDetailedInfo) {
  const fullInfo = await get_node_info("nodes-base.slack");
}

// ❌ 避免：直接获取完整信息
const fullInfo = await get_node_info("nodes-base.slack"); // 100KB+
```

### 2. 验证策略

```javascript
// ✅ 开发阶段：使用ai-friendly模式
validate_node_operation(nodeType, config, {profile: "ai-friendly"});

// ✅ 生产部署：使用strict模式
validate_node_operation(nodeType, config, {profile: "strict"});

// ✅ 快速检查：使用minimal验证
validate_node_minimal(nodeType, config);
```

### 3. 工作流构建

```javascript
// ✅ 推荐的构建顺序
// 1. 设计节点
const nodes = await planWorkflowNodes();

// 2. 验证单个节点
for (const node of nodes) {
  await validate_node_operation(node.type, node.parameters);
}

// 3. 构建连接
const workflow = buildWorkflowWithConnections(nodes);

// 4. 验证整体工作流
await validate_workflow(workflow);
```

### 4. 错误处理

```javascript
// ✅ 使用现代错误处理模式
const errorHandlingTemplate = await get_node_for_task(
  "modern_error_handling_patterns"
);

// ✅ 为关键节点添加错误处理
const nodeWithErrorHandling = {
  ...baseNode,
  onError: "continueErrorOutput",
  retryOnFail: true,
  maxTries: 3
};
```

## 🔧 故障排查

### 常见问题与解决方案

#### 1. 节点配置验证失败

**问题**：`validate_node_operation` 返回错误
**解决方案**：
```javascript
// 检查必需字段
const validation = await validate_node_minimal(nodeType, config);
if (!validation.valid) {
  console.log("缺少必需字段:", validation.missingFields);
}

// 获取属性依赖
const dependencies = await get_property_dependencies(nodeType);
console.log("属性依赖关系:", dependencies);
```

#### 2. 工作流连接错误

**问题**：节点连接验证失败
**解决方案**：
```javascript
// 单独验证连接
const connectionValidation = await validate_workflow_connections(workflow);

// 检查连接格式
// ✅ 正确：使用节点名称
"connections": {
  "HTTP Request": {
    "main": [[{"node": "Set", "type": "main", "index": 0}]]
  }
}

// ❌ 错误：使用节点ID
"connections": {
  "http-request-1": { ... }
}
```

#### 3. AI工具配置问题

**问题**：节点无法作为AI工具使用
**解决方案**：
```javascript
// 检查AI工具兼容性
const toolInfo = await get_node_as_tool_info("nodes-base.googleSheets");

// 确保正确的工具配置
const toolConfig = {
  name: "Google Sheets Tool",
  description: "Read and write Google Sheets data",
  schema: toolInfo.schema
};
```

### 调试技巧

#### 1. 使用分步验证

```javascript
// 分步验证工作流
const steps = [
  () => validate_workflow_connections(workflow),
  () => validate_workflow_expressions(workflow),
  () => validate_workflow(workflow, {validateNodes: false}),
  () => validate_workflow(workflow)
];

for (const [index, step] of steps.entries()) {
  try {
    const result = await step();
    console.log(`步骤 ${index + 1} 通过:`, result);
  } catch (error) {
    console.log(`步骤 ${index + 1} 失败:`, error);
    break;
  }
}
```

#### 2. 属性搜索调试

```javascript
// 查找特定属性
const authProperties = await search_node_properties(
  "nodes-base.slack", 
  "auth"
);

// 查找所有可用操作
const operations = await search_node_properties(
  "nodes-base.slack", 
  "operation"
);
```

## 🎯 进阶功能

### 1. 自定义工作流模板

```javascript
// 创建自定义任务模板
const customTemplate = {
  name: "数据同步工作流",
  description: "从API获取数据并同步到数据库",
  nodes: [
    // 定时触发器
    {
      name: "定时触发",
      type: "n8n-nodes-base.cron",
      parameters: {
        triggerTimes: {
          item: [{hour: 9, minute: 0}]
        }
      }
    },
    // API调用
    {
      name: "获取数据",
      type: "n8n-nodes-base.httpRequest",
      parameters: {
        method: "GET",
        url: "https://api.example.com/data"
      }
    },
    // 数据处理
    {
      name: "处理数据",
      type: "n8n-nodes-base.code",
      parameters: {
        language: "javascript",
        jsCode: `
          const processedData = $input.all().map(item => ({
            id: item.json.id,
            name: item.json.name,
            processedAt: new Date().toISOString()
          }));
          return processedData;
        `
      }
    },
    // 数据库插入
    {
      name: "保存数据",
      type: "n8n-nodes-base.postgres",
      parameters: {
        operation: "insert",
        table: "sync_data",
        columns: "id, name, processed_at"
      }
    }
  ]
};

// 验证自定义模板
const templateValidation = await validate_workflow(customTemplate);
```

### 2. 动态节点配置

```javascript
// 根据条件动态配置节点
async function createDynamicNode(nodeType, requirements) {
  // 获取节点基本信息
  const nodeInfo = await get_node_essentials(nodeType);
  
  // 分析属性依赖
  const dependencies = await get_property_dependencies(nodeType);
  
  // 构建动态配置
  const config = {};
  
  // 根据需求设置必需属性
  for (const requirement of requirements) {
    if (nodeInfo.properties[requirement.property]) {
      config[requirement.property] = requirement.value;
    }
  }
  
  // 验证配置
  const validation = await validate_node_operation(nodeType, config);
  
  if (!validation.valid) {
    // 自动修复配置
    for (const error of validation.errors) {
      if (error.fix) {
        // 应用建议的修复
        Object.assign(config, error.fix);
      }
    }
  }
  
  return config;
}

// 使用示例
const slackConfig = await createDynamicNode("nodes-base.slack", [
  {property: "resource", value: "message"},
  {property: "operation", value: "post"},
  {property: "channel", value: "#general"}
]);
```

### 3. 批量工作流管理

```javascript
// 批量创建和验证工作流
async function batchCreateWorkflows(templates) {
  const results = [];
  
  for (const template of templates) {
    try {
      // 验证模板
      const validation = await validate_workflow(template);
      
      if (validation.valid) {
        // 如果配置了n8n API，直接创建
        if (process.env.N8N_API_URL) {
          const created = await n8n_create_workflow(template);
          results.push({
            name: template.name,
            status: "created",
            id: created.id
          });
        } else {
          results.push({
            name: template.name,
            status: "validated",
            template: template
          });
        }
      } else {
        results.push({
          name: template.name,
          status: "failed",
          errors: validation.errors
        });
      }
    } catch (error) {
      results.push({
        name: template.name,
        status: "error",
        error: error.message
      });
    }
  }
  
  return results;
}
```

### 4. 智能节点推荐

```javascript
// 基于需求推荐节点
async function recommendNodes(requirements) {
  const recommendations = [];
  
  // 搜索相关节点
  const searchResults = await search_nodes({
    query: requirements.keywords.join(" "),
    mode: "OR",
    limit: 20
  });
  
  // 按类别筛选
  if (requirements.category) {
    const categoryNodes = await list_nodes({
      category: requirements.category,
      limit: 50
    });
    
    // 合并结果
    const combined = [...searchResults, ...categoryNodes];
    const unique = Array.from(
      new Map(combined.map(node => [node.name, node])).values()
    );
    
    recommendations.push(...unique);
  }
  
  // 如果需要AI功能，优先推荐AI工具
  if (requirements.needsAI) {
    const aiTools = await list_ai_tools();
    recommendations.unshift(...aiTools.slice(0, 5));
  }
  
  // 为每个推荐节点获取基本信息
  const detailedRecommendations = await Promise.all(
    recommendations.slice(0, 10).map(async (node) => {
      const essentials = await get_node_essentials(node.name);
      return {
        ...node,
        essentials: essentials
      };
    })
  );
  
  return detailedRecommendations;
}

// 使用示例
const recommendations = await recommendNodes({
  keywords: ["email", "notification"],
  category: "output",
  needsAI: false
});
```

## 📚 参考资源

### 官方文档
- [n8n官方文档](https://docs.n8n.io/)
- [n8n节点参考](https://docs.n8n.io/integrations/)
- [MCP协议规范](https://modelcontextprotocol.io/)

### 社区资源
- [n8n社区论坛](https://community.n8n.io/)
- [n8n GitHub仓库](https://github.com/n8n-io/n8n)
- [工作流模板库](https://n8n.io/workflows/)

### 开发工具
- [n8n-MCP GitHub](https://github.com/czlonkowski/n8n-mcp)
- [n8n本地开发环境](https://docs.n8n.io/hosting/installation/npm/)

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**兼容版本**：n8n v1.103.2+  

> 💡 **提示**：本指南涵盖了n8n-MCP的核心功能和使用方法。如需了解特定节点的详细配置，请使用相应的MCP工具获取最新信息。
