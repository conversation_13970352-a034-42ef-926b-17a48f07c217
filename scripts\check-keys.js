#!/usr/bin/env node

/**
 * 查看数据库中的密钥数据
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function checkKeys() {
  try {
    console.log('🔍 查看数据库中的密钥数据...\n');
    
    // 查看所有密钥
    const { data: keys, error: keysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        *,
        tutorials (title, status)
      `)
      .order('created_at', { ascending: false });
    
    if (keysError) {
      console.error('❌ 查询密钥失败:', keysError);
      return;
    }
    
    if (!keys || keys.length === 0) {
      console.log('❌ 数据库中没有密钥数据');
      console.log('💡 需要先运行种子数据脚本添加测试密钥');
      return;
    }
    
    console.log(`✅ 找到 ${keys.length} 个密钥:`);
    console.log('');
    
    keys.forEach((key, index) => {
      console.log(`${index + 1}. 密钥信息:`);
      console.log(`   密钥代码: ${key.key_code}`);
      console.log(`   状态: ${key.status}`);
      console.log(`   教程ID: ${key.tutorial_id}`);
      console.log(`   教程标题: ${key.tutorials?.title || '未知'}`);
      console.log(`   教程状态: ${key.tutorials?.status || '未知'}`);
      console.log(`   创建时间: ${key.created_at}`);
      if (key.used_at) {
        console.log(`   使用时间: ${key.used_at}`);
        console.log(`   使用者: ${key.user_identifier || '未知'}`);
      }
      console.log('');
    });
    
    // 查看用户解锁记录
    const { data: unlocks, error: unlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select(`
        *,
        tutorials (title)
      `)
      .order('unlocked_at', { ascending: false });
    
    if (unlocksError) {
      console.error('❌ 查询解锁记录失败:', unlocksError);
      return;
    }
    
    console.log(`📊 用户解锁记录: ${unlocks?.length || 0} 条`);
    if (unlocks && unlocks.length > 0) {
      unlocks.forEach((unlock, index) => {
        console.log(`${index + 1}. ${unlock.user_identifier} 解锁了 "${unlock.tutorials?.title}" (${unlock.unlocked_at})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 查询异常:', error);
  }
}

checkKeys();