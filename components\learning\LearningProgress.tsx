'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Clock, Trophy, Target, CheckCircle, PlayCircle } from 'lucide-react'

interface LearningProgressProps {
  tutorialId: number
  userId?: string
  className?: string
}

interface ProgressData {
  totalSections: number
  completedSections: number
  totalTime: number
  studyTime: number
  achievements: Achievement[]
  currentSection?: Section
}

interface Achievement {
  id: number
  title: string
  description: string
  icon: string
  unlockedAt: string
}

interface Section {
  id: number
  title: string
  duration: number
  completed: boolean
}

export function LearningProgress({ tutorialId, userId, className = '' }: LearningProgressProps) {
  const [progress, setProgress] = useState<ProgressData>({
    totalSections: 0,
    completedSections: 0,
    totalTime: 0,
    studyTime: 0,
    achievements: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadProgress()
  }, [tutorialId, userId])

  const loadProgress = async () => {
    try {
      // 模拟API调用，实际应该调用学习进度API
      const mockProgress: ProgressData = {
        totalSections: 8,
        completedSections: 3,
        totalTime: 120, // 分钟
        studyTime: 45,
        achievements: [
          {
            id: 1,
            title: '首次学习',
            description: '完成第一个章节',
            icon: '🎯',
            unlockedAt: '2025-01-20'
          },
          {
            id: 2,
            title: '坚持学习',
            description: '连续学习3天',
            icon: '🔥',
            unlockedAt: '2025-01-22'
          }
        ],
        currentSection: {
          id: 4,
          title: '第4章：高级技巧',
          duration: 25,
          completed: false
        }
      }

      // 模拟加载延迟
      setTimeout(() => {
        setProgress(mockProgress)
        setLoading(false)
      }, 500)

    } catch (error) {
      console.error('加载学习进度失败:', error)
      setLoading(false)
    }
  }

  const progressPercentage = progress.totalSections > 0 
    ? Math.round((progress.completedSections / progress.totalSections) * 100)
    : 0

  const timeProgressPercentage = progress.totalTime > 0
    ? Math.round((progress.studyTime / progress.totalTime) * 100)
    : 0

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">加载学习进度中...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 总体progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            学习进度
          </CardTitle>
          <CardDescription>
            跟踪您的学习进度和成就
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 章节进度 */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">章节完成度</span>
              <span className="text-sm text-gray-600">
                {progress.completedSections} / {progress.totalSections}
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between items-center mt-1">
              <span className="text-sm text-gray-500">已完成 {progressPercentage}%</span>
              <Badge variant={progressPercentage === 100 ? "default" : "secondary"}>
                {progressPercentage === 100 ? "已完成" : "学习中"}
              </Badge>
            </div>
          </div>

          {/* 学习时间 */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                学习时间
              </span>
              <span className="text-sm text-gray-600">
                {progress.studyTime} / {progress.totalTime} 分钟
              </span>
            </div>
            <Progress value={timeProgressPercentage} className="h-2" />
            <div className="text-sm text-gray-500 mt-1">
              预计还需 {progress.totalTime - progress.studyTime} 分钟完成
            </div>
          </div>

          {/* 当前章节 */}
          {progress.currentSection && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-blue-900">正在学习</h4>
                  <p className="text-blue-700">{progress.currentSection.title}</p>
                  <p className="text-sm text-blue-600">
                    预计时长：{progress.currentSection.duration} 分钟
                  </p>
                </div>
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <PlayCircle className="h-4 w-4 mr-2" />
                  继续学习
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 成就系统 */}
      {progress.achievements.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              学习成就
            </CardTitle>
            <CardDescription>
              您已获得 {progress.achievements.length} 个成就
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {progress.achievements.map((achievement) => (
                <div 
                  key={achievement.id}
                  className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"
                >
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-yellow-900">{achievement.title}</h4>
                    <p className="text-sm text-yellow-700">{achievement.description}</p>
                    <p className="text-xs text-yellow-600 mt-1">
                      获得于 {new Date(achievement.unlockedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-yellow-600" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 学习建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            学习建议
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p className="flex items-center gap-2">
              💡 建议每天学习15-30分钟，保持学习连续性
            </p>
            <p className="flex items-center gap-2">
              📝 完成每章节的练习题，巩固学习效果
            </p>
            <p className="flex items-center gap-2">
              🤝 加入学习讨论，与其他学员交流心得
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}