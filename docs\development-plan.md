# 知识商城后续开发规划文档

## 项目概述

基于 Next.js 14 + Supabase 的知识商城系统，实现教程内容的密钥验证和销售功能。本文档详细规划从当前状态到完整可运营系统的开发路径。

## 当前状态评估

### 技术栈
- ✅ **前端**: Next.js 14 + React 18 + TypeScript
- ✅ **UI组件**: shadcn/ui + Radix UI + Tailwind CSS
- ✅ **表单处理**: React Hook Form + Zod
- ⚠️ **数据库**: PostgreSQL (需迁移到 Supabase)
- ⚠️ **认证**: 当前为演示模式
- ❌ **部署**: 需要配置生产环境

### 功能完成度
- ✅ **基础UI框架**: 90%
- ✅ **数据库设计**: 95%
- ✅ **密钥系统**: 80%
- ⚠️ **用户认证**: 30%
- ⚠️ **内容管理**: 60%
- ❌ **支付集成**: 0%

---

## 阶段一：基础设施迁移 (第1-2周)

### 1.1 Supabase 数据库迁移

#### 📋 任务清单
- [ ] **创建 Supabase 项目**
  - [ ] 注册 Supabase 账户
  - [ ] 创建新项目 (区域选择: 新加坡)
  - [ ] 获取项目连接信息
  - [ ] 配置安全策略

- [ ] **数据库结构迁移**
  - [ ] 执行现有SQL脚本 (`scripts/01-create-tables.sql`)
  - [ ] 执行种子数据 (`scripts/02-seed-data.sql`)
  - [ ] 验证表结构和关系
  - [ ] 设置数据库备份策略

- [ ] **连接配置更新**
  - [ ] 更新 `lib/database.ts` 配置
  - [ ] 添加 Supabase 客户端配置
  - [ ] 更新环境变量配置
  - [ ] 测试数据库连接

#### 🔧 技术实施
```bash
# 安装 Supabase 依赖
pnpm add @supabase/supabase-js

# 环境变量配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### ⚠️ 注意事项
- Supabase 免费层限制：500MB 存储，2GB 带宽/月
- Row Level Security (RLS) 策略需要仔细配置
- 备份频率建议设置为每日

### 1.2 认证系统重构

#### 📋 任务清单
- [ ] **Supabase Auth 集成**
  - [ ] 配置 Supabase 认证策略
  - [ ] 实现邮箱+密码登录
  - [ ] 添加社交登录选项
  - [ ] 设置用户角色权限

- [ ] **管理员认证修复**
  - [ ] 移除演示模式代码
  - [ ] 实现真实的管理员验证
  - [ ] 添加 JWT 令牌验证
  - [ ] 实现会话管理

- [ ] **权限控制系统**
  - [ ] 设计用户角色体系
  - [ ] 实现 API 权限中间件
  - [ ] 添加前端路由保护
  - [ ] 实现操作日志记录

#### 🔧 技术实施
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)
```

---

## 阶段二：核心功能完善 (第3-5周)

### 2.1 教程内容管理系统

#### 📋 任务清单
- [ ] **富文本编辑器集成**
  - [ ] 选择编辑器方案 (推荐: Tiptap)
  - [ ] 实现教程内容编辑
  - [ ] 添加图片上传功能
  - [ ] 支持 Markdown 导入/导出

- [ ] **内容版本管理**
  - [ ] 实现内容版本控制
  - [ ] 添加草稿/发布状态
  - [ ] 实现内容预览功能
  - [ ] 添加内容审核流程

- [ ] **媒体资源管理**
  - [ ] 配置 Supabase Storage
  - [ ] 实现图片/视频上传
  - [ ] 添加文件压缩优化
  - [ ] 实现 CDN 分发

#### 🔧 技术实施
```bash
# 安装富文本编辑器
pnpm add @tiptap/react @tiptap/starter-kit
```

### 2.2 用户学习进度系统

#### 📋 任务清单
- [ ] **进度跟踪模型**
  - [ ] 设计学习进度数据结构
  - [ ] 实现章节完成状态
  - [ ] 添加学习时长统计
  - [ ] 实现学习路径规划

- [ ] **互动功能**
  - [ ] 添加书签/收藏功能
  - [ ] 实现笔记功能
  - [ ] 添加评论系统
  - [ ] 实现问答互动

- [ ] **个性化推荐**
  - [ ] 实现基础推荐算法
  - [ ] 添加相关教程推荐
  - [ ] 实现学习统计报告
  - [ ] 添加成就系统

---

## 阶段三：商业化功能 (第6-8周)

### 3.1 支付系统集成

#### 📋 任务清单
- [ ] **支付宝集成**
  - [ ] 申请支付宝开发者账户
  - [ ] 集成当面付 API
  - [ ] 实现订单管理系统
  - [ ] 添加支付状态回调

- [ ] **微信支付集成**
  - [ ] 申请微信支付商户号
  - [ ] 集成微信支付 API
  - [ ] 实现扫码支付功能
  - [ ] 添加支付成功通知

- [ ] **订单管理系统**
  - [ ] 设计订单数据模型
  - [ ] 实现订单状态管理
  - [ ] 添加退款处理逻辑
  - [ ] 实现财务对账功能

#### 💰 成本估算
- 支付宝当面付：免费
- 微信支付：300元认证费
- 总计：300元 (一次性)

### 3.2 营销和分析系统

#### 📋 任务清单
- [ ] **用户行为分析**
  - [ ] 集成免费分析工具 (Google Analytics)
  - [ ] 实现用户行为追踪
  - [ ] 添加转化漏斗分析
  - [ ] 生成运营数据报告

- [ ] **营销工具**
  - [ ] 实现优惠券系统
  - [ ] 添加推荐奖励机制
  - [ ] 实现限时折扣功能
  - [ ] 添加邮件营销集成

- [ ] **SEO 优化**
  - [ ] 优化页面 Meta 信息
  - [ ] 实现 sitemap 生成
  - [ ] 添加结构化数据
  - [ ] 优化页面加载速度

---

## 阶段四：运营优化 (第9-10周)

### 4.1 性能优化

#### 📋 任务清单
- [ ] **前端性能优化**
  - [ ] 实现代码分割
  - [ ] 添加图片懒加载
  - [ ] 优化 Bundle 大小
  - [ ] 实现服务端缓存

- [ ] **数据库优化**
  - [ ] 添加查询索引
  - [ ] 实现连接池优化
  - [ ] 添加查询缓存
  - [ ] 优化 N+1 查询问题

- [ ] **CDN 和缓存**
  - [ ] 配置 Cloudflare CDN
  - [ ] 实现静态资源缓存
  - [ ] 添加 API 响应缓存
  - [ ] 优化图片传输

### 4.2 监控和维护

#### 📋 任务清单
- [ ] **系统监控**
  - [ ] 集成错误监控 (Sentry 免费层)
  - [ ] 添加性能监控
  - [ ] 实现健康检查接口
  - [ ] 设置告警通知

- [ ] **自动化部署**
  - [ ] 配置 CI/CD 流水线
  - [ ] 实现自动化测试
  - [ ] 添加部署脚本
  - [ ] 设置回滚机制

- [ ] **数据备份**
  - [ ] 设置自动数据备份
  - [ ] 实现备份恢复测试
  - [ ] 添加数据迁移脚本
  - [ ] 配置灾难恢复计划

---

## 技术架构规划

### 数据库架构 (Supabase)
```sql
-- 用户表 (使用 Supabase Auth)
-- 教程表 (tutorials)
-- 分类表 (categories)  
-- 密钥表 (tutorial_keys)
-- 解锁记录表 (user_unlocks)
-- 学习进度表 (user_progress) -- 新增
-- 订单表 (orders) -- 新增
```

### API架构设计
```
/api
├── auth/           # 认证相关
├── admin/          # 管理员功能
├── public/         # 公开接口
├── user/           # 用户功能
├── payment/        # 支付相关 -- 新增
└── analytics/      # 数据分析 -- 新增
```

---

## 风险评估与应对策略

### 技术风险
| 风险项 | 影响度 | 概率 | 应对策略 |
|--------|--------|------|----------|
| Supabase 免费额度超限 | 高 | 中 | 监控使用量，准备升级计划 |
| 支付接口申请失败 | 中 | 低 | 准备备选支付方案 |
| 性能瓶颈 | 中 | 中 | 提前进行性能测试 |

### 业务风险
| 风险项 | 影响度 | 概率 | 应对策略 |
|--------|--------|------|----------|
| 用户增长缓慢 | 高 | 中 | 制定营销推广计划 |
| 内容质量不足 | 高 | 中 | 建立内容审核机制 |
| 竞争对手冲击 | 中 | 中 | 强化产品差异化 |

---

## 资源需求

### 开发资源
- **开发时间**: 10-12周
- **开发人员**: 1-2名全栈开发者
- **设计资源**: 外包设计或使用现有模板

### 运营成本 (月度)
- **Supabase**: 免费层 (可扩展至 $25/月)
- **Cloudflare**: 免费层
- **域名**: 已有 ($0)
- **服务器**: 已有 ($0)
- **监控工具**: 免费层
- **总计**: $0-25/月

---

## 成功指标

### 技术指标
- [ ] 页面加载时间 < 3秒
- [ ] API 响应时间 < 200ms
- [ ] 系统可用性 > 99.5%
- [ ] 移动端适配完成度 100%

### 业务指标
- [ ] 月活跃用户 > 1000
- [ ] 教程完成率 > 60%
- [ ] 用户留存率 > 40%
- [ ] 月收入 > 5000元

---

## 下一步行动

### 本周任务 (优先级)
1. **高优先级**: 创建 Supabase 项目并完成数据库迁移
2. **高优先级**: 修复认证系统的安全问题
3. **中优先级**: 实现教程内容的真实渲染
4. **低优先级**: 开始支付系统的需求调研

### 需要决策的事项
- [ ] 选择富文本编辑器方案
- [ ] 确定支付接口优先级 (支付宝 vs 微信)
- [ ] 决定是否需要移动端 App
- [ ] 确定内容审核流程

---

*本文档会根据开发进度持续更新，请定期检查最新版本。*

**文档版本**: v1.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**负责人**: 开发团队