#!/usr/bin/env node

/**
 * 数据库完整性检查脚本
 * 检查所有必需的表是否存在，API路由是否正常工作
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// 核心表检查列表
const REQUIRED_TABLES = [
  'categories',
  'tutorials', 
  'tutorial_keys',
  'user_unlocks',
  'system_config'
];

// 新功能表检查列表
const NEW_FEATURE_TABLES = [
  'tutorial_media',
  'tutorial_sections',
  'user_learning_records',
  'user_learning_stats',
  'learning_achievements',
  'user_achievements',
  'learning_sessions'
];

async function checkTableExists(tableName) {
  try {
    const { error } = await supabaseAdmin
      .from(tableName)
      .select('*')
      .limit(1);
    
    return !error;
  } catch (error) {
    return false;
  }
}

async function checkDatabaseStatus() {
  console.log('🔍 数据库完整性检查\n');
  
  // 检查核心表
  console.log('📋 核心表状态:');
  let coreTablesCount = 0;
  for (const table of REQUIRED_TABLES) {
    const exists = await checkTableExists(table);
    console.log(`${exists ? '✅' : '❌'} ${table}`);
    if (exists) coreTablesCount++;
  }
  
  // 检查新功能表
  console.log('\n🆕 新功能表状态:');
  let newTablesCount = 0;
  for (const table of NEW_FEATURE_TABLES) {
    const exists = await checkTableExists(table);
    console.log(`${exists ? '✅' : '❌'} ${table}`);
    if (exists) newTablesCount++;
  }
  
  // 汇总报告
  console.log('\n📊 数据库状态总结:');
  console.log(`核心表: ${coreTablesCount}/${REQUIRED_TABLES.length} 完成`);
  console.log(`新功能表: ${newTablesCount}/${NEW_FEATURE_TABLES.length} 完成`);
  
  const coreComplete = coreTablesCount === REQUIRED_TABLES.length;
  const newFeaturesComplete = newTablesCount === NEW_FEATURE_TABLES.length;
  
  if (coreComplete && newFeaturesComplete) {
    console.log('🎉 所有数据库表已准备就绪');
  } else if (coreComplete) {
    console.log('⚠️  核心功能正常，但新功能表缺失');
    console.log('💡 需要在Supabase Dashboard中执行创建脚本');
  } else {
    console.log('🚨 核心表缺失，系统无法正常运行');
  }
  
  // 如果有缺失的表，提供创建指导
  const missingTables = [...REQUIRED_TABLES, ...NEW_FEATURE_TABLES].filter(async (table) => {
    return !(await checkTableExists(table));
  });
  
  if (missingTables.length > 0) {
    console.log('\n📝 缺失表的创建脚本位置:');
    console.log('- 核心表: scripts/01-create-tables.sql');
    console.log('- 媒体管理: scripts/05-media-management.sql');
    console.log('- 学习进度: scripts/08-learning-progress-tables.sql');
    console.log('\n请在Supabase Dashboard的SQL编辑器中按顺序执行这些脚本');
  }
}

// 检查API可用性
async function checkAPIRoutes() {
  console.log('\n🔗 API路由检查:');
  
  const routes = [
    'http://localhost:3001/api/public/tutorials',
    'http://localhost:3001/api/public/categories', 
    'http://localhost:3001/api/user-unlocks'
  ];
  
  for (const route of routes) {
    try {
      const response = await fetch(route);
      const status = response.status;
      console.log(`${status === 200 ? '✅' : '❌'} ${route} (${status})`);
    } catch (error) {
      console.log(`❌ ${route} (连接失败)`);
    }
  }
}

async function main() {
  try {
    await checkDatabaseStatus();
    await checkAPIRoutes();
    
    console.log('\n🔧 如果网站无法访问，请检查:');
    console.log('1. 开发服务器是否在正确端口运行 (npm run dev)');
    console.log('2. 浏览器访问 http://localhost:3001');
    console.log('3. 数据库表是否已创建完成');
    console.log('4. .env.local 环境变量是否正确配置');
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error);
  }
}

main();