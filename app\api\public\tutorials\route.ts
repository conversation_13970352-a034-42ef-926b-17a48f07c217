import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { CacheManager, CACHE_TAGS } from "@/lib/cache-manager"

// ==========================================
// 教程API v3 - 智能缓存版本
// 支持分页、搜索、筛选、排序 + 智能缓存失效
// ==========================================

interface TutorialsQuery {
  page?: number
  limit?: number
  search?: string
  category?: string
  tags?: string[]
  sort?: 'created_at' | 'title' | 'price'
  order?: 'asc' | 'desc'
}

interface APIResponse<T> {
  success: boolean
  data: T
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  error?: string
}

/**
 * 解析和验证查询参数
 */
function parseQueryParams(request: NextRequest): TutorialsQuery {
  const { searchParams } = new URL(request.url)
  
  return {
    page: Math.max(1, parseInt(searchParams.get('page') || '1')),
    limit: Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '12'))),
    search: searchParams.get('search') || undefined,
    category: searchParams.get('category') || undefined,
    tags: searchParams.get('tags')?.split(',').filter(Boolean) || undefined,
    sort: (searchParams.get('sort') as any) || 'created_at',
    order: (searchParams.get('order') as any) || 'desc'
  }
}

/**
 * 构建Supabase查询
 */
function buildQuery(params: TutorialsQuery) {
  let query = supabaseAdmin
    .from('tutorials')
    .select(`
      id,
      title,
      description,
      price,
      tags,
      created_at,
      updated_at,
      thumbnail_url,
      categories!inner(
        id,
        name,
        description
      )
    `, { count: 'exact' })
    .eq('status', 'published')

  // 搜索功能
  if (params.search) {
    query = query.or(`title.ilike.%${params.search}%,description.ilike.%${params.search}%`)
  }

  // 分类筛选
  if (params.category) {
    query = query.eq('categories.name', params.category)
  }

  // 标签筛选
  if (params.tags && params.tags.length > 0) {
    query = query.overlaps('tags', params.tags)
  }

  // 排序
  const ascending = params.order === 'asc'
  query = query.order(params.sort!, { ascending })

  // 分页
  const offset = (params.page! - 1) * params.limit!
  query = query.range(offset, offset + params.limit! - 1)

  return query
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 教程API查询开始 - 智能缓存版本')
    
    // 解析查询参数
    const params = parseQueryParams(request)
    console.log('📋 查询参数:', params)

    // 构建和执行查询
    const { data: tutorials, error, count } = await buildQuery(params)

    if (error) {
      console.error('❌ Supabase查询错误:', error)
      throw error
    }

    // 格式化教程数据
    const formattedTutorials = tutorials?.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      tags: tutorial.tags || [],
      created_at: tutorial.created_at,
      updated_at: tutorial.updated_at,
      thumbnail_url: tutorial.thumbnail_url,
      category: {
        id: tutorial.categories.id,
        name: tutorial.categories.name,
        description: tutorial.categories.description
      },
      // 向后兼容
      category_name: tutorial.categories.name
    })) || []

    // 计算分页信息
    const total = count || 0
    const totalPages = Math.ceil(total / params.limit!)
    
    const pagination = {
      page: params.page!,
      limit: params.limit!,
      total,
      totalPages,
      hasNext: params.page! < totalPages,
      hasPrev: params.page! > 1
    }

    // 标准化API响应
    const response = {
      success: true,
      data: formattedTutorials,
      pagination
    }

    console.log(`✅ 返回 ${formattedTutorials.length}/${total} 个教程`)

    // 🚀 智能缓存策略
    const cacheStrategy = CacheManager.selectCacheStrategy(request, 'tutorials')
    
    // 准备缓存标签
    const cacheTags = [
      CACHE_TAGS.TUTORIALS_PUBLIC,
      CACHE_TAGS.TUTORIALS_ALL
    ]
    
    // 添加搜索和筛选标签
    if (params.search) {
      cacheTags.push(CACHE_TAGS.TUTORIALS_SEARCH)
    }
    if (params.category) {
      cacheTags.push(CACHE_TAGS.TUTORIALS_FILTER)
    }
    
    // 添加分类特定标签
    if (params.category) {
      const categoryTutorials = formattedTutorials.filter(t => t.category_name === params.category)
      if (categoryTutorials.length > 0) {
        cacheTags.push(CACHE_TAGS.CATEGORY_TUTORIALS(categoryTutorials[0].category.id))
      }
    }

    // 返回带智能缓存的响应
    return CacheManager.createCachedResponse(
      response,
      cacheStrategy,
      cacheTags,
      {
        'X-Total-Count': total.toString(),
        'X-Page': params.page!.toString(),
        'X-Per-Page': params.limit!.toString(),
        'X-Cache-Strategy': cacheStrategy,
        'X-Is-Admin-Request': CacheManager.isAdminRequest(request).toString()
      }
    )

  } catch (error) {
    console.error('❌ 教程API异常:', error)
    
    const errorResponse = {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : '获取教程列表失败'
    }

    return NextResponse.json(errorResponse, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache',
        'X-Error': 'true'
      }
    })
  }
}
