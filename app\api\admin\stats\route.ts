import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET() {
  try {
    // 获取统计数据 - 使用Supabase客户端
    const [tutorialsResult, keysResult, usedKeysResult, unlocksResult] = await Promise.all([
      supabaseAdmin.from('tutorials').select('id', { count: 'exact', head: true }),
      supabaseAdmin.from('tutorial_keys').select('id', { count: 'exact', head: true }),
      supabaseAdmin.from('tutorial_keys').select('id', { count: 'exact', head: true }).eq('status', 'used'),
      supabaseAdmin.from('user_unlocks').select('id', { count: 'exact', head: true })
    ])

    // 检查是否有错误
    if (tutorialsResult.error) throw tutorialsResult.error
    if (keysResult.error) throw keysResult.error
    if (usedKeysResult.error) throw usedKeysResult.error
    if (unlocksResult.error) throw unlocksResult.error

    // 计算总营收：获取所有已解锁教程的价格总和
    const { data: revenueData, error: revenueError } = await supabaseAdmin
      .from('user_unlocks')
      .select(`
        tutorial_id,
        tutorials!inner(price)
      `)

    if (revenueError) throw revenueError

    // 计算总营收
    const totalRevenue = revenueData?.reduce((sum, unlock) => {
      return sum + (unlock.tutorials.price || 0)
    }, 0) || 0

    return NextResponse.json({
      total_tutorials: tutorialsResult.count || 0,
      total_keys: keysResult.count || 0,
      used_keys: usedKeysResult.count || 0,
      total_unlocks: unlocksResult.count || 0,
      total_revenue: totalRevenue // 新增总营收字段
    })
  } catch (error) {
    console.error("Get stats error:", error)
    return NextResponse.json({ error: "Failed to fetch stats" }, { status: 500 })
  }
}
