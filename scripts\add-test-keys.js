#!/usr/bin/env node

/**
 * 在Supabase中添加测试密钥数据
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function addTestKeys() {
  try {
    console.log('🔑 添加测试密钥数据...\n');
    
    // 直接使用 Supabase 客户端插入数据
    const testKeys = [
      // Next.js 教程的密钥
      { tutorial_id: 1, key_code: 'NEXTJS-2024-ABC123', status: 'unused', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() },
      { tutorial_id: 1, key_code: 'NEXTJS-2024-DEF456', status: 'unused', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() },
      { tutorial_id: 1, key_code: 'NEXTJS-2024-GHI789', status: 'used', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() },
      
      // UI/UX 设计教程的密钥
      { tutorial_id: 2, key_code: 'DESIGN-2024-JKL012', status: 'unused', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() },
      { tutorial_id: 2, key_code: 'DESIGN-2024-MNO345', status: 'unused', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() },
      { tutorial_id: 2, key_code: 'DESIGN-2024-PQR678', status: 'unused', expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() }
    ];
    
    // 插入密钥数据
    const { data: insertedKeys, error: insertError } = await supabaseAdmin
      .from('tutorial_keys')
      .upsert(testKeys, { onConflict: 'key_code' })
      .select();
    
    if (insertError) {
      console.error('❌ 插入密钥失败:', insertError);
      return;
    }
    
    console.log(`✅ 成功插入 ${insertedKeys.length} 个测试密钥`);
    
    // 查看插入结果
    const { data: keys, error: selectError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        key_code,
        status,
        expires_at,
        created_at,
        tutorials (title)
      `)
      .order('created_at', { ascending: false });
    
    if (selectError) {
      console.error('❌ 查询密钥失败:', selectError);
      return;
    }
    
    console.log('\n📋 当前所有密钥:');
    keys.forEach((key, index) => {
      console.log(`${index + 1}. ${key.key_code} (${key.status}) - ${key.tutorials?.title || '未知教程'}`);
      console.log(`   过期时间: ${key.expires_at}`);
      console.log(`   创建时间: ${key.created_at}`);
      console.log('');
    });
    
    console.log('🎉 测试密钥数据添加完成！');
    console.log('\n💡 可用于测试的密钥:');
    console.log('   - NEXTJS-2024-ABC123 (Next.js 教程)');
    console.log('   - NEXTJS-2024-DEF456 (Next.js 教程)');
    console.log('   - DESIGN-2024-JKL012 (UI/UX 设计教程)');
    console.log('   - DESIGN-2024-MNO345 (UI/UX 设计教程)');
    console.log('   - DESIGN-2024-PQR678 (UI/UX 设计教程)');
    
  } catch (error) {
    console.error('❌ 添加测试密钥异常:', error);
  }
}

addTestKeys();