"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ArrowLeft, Save, Eye, Plus, X, BookOpen, Calendar, Settings } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { TutorialEditor } from "@/components/editor/TutorialEditor"
import { HTMLTutorialEditor } from "@/components/editor/HTMLTutorialEditor"
import { ErrorBoundary } from '@/components/ui/error-boundary'

interface Category {
  id: number
  name: string
  description: string
}

export default function CreateTutorialPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  // 检测编辑模式
  const editId = searchParams.get('edit')
  const isEditMode = !!editId
  
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [previewMode, setPreviewMode] = useState(false)

  // 对话框状态
  const [showCategoryDialog, setShowCategoryDialog] = useState(false)
  const [showTagDialog, setShowTagDialog] = useState(false)

  // 新分类表单
  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
  })

  // 预设标签和自定义标签
  const [presetTags] = useState([
    "React",
    "Next.js",
    "TypeScript",
    "JavaScript",
    "Node.js",
    "Python",
    "Java",
    "UI设计",
    "UX设计",
    "Figma",
    "Photoshop",
    "设计系统",
    "营销策略",
    "数字营销",
    "品牌建设",
    "社交媒体",
    "生活技能",
    "健康养生",
    "理财投资",
    "时间管理",
  ])
  const [customTagInput, setCustomTagInput] = useState("")

  const [tutorial, setTutorial] = useState({
    title: "",
    description: "",
    content: "",
    category_id: "",
    tags: [] as string[],
    price: "",
    created_date: new Date().toISOString().split("T")[0], // 默认今天
    status: "draft" as "draft" | "published",
  })

  const [newTag, setNewTag] = useState("")

  useEffect(() => {
    loadCategories()
    // 如果是编辑模式，加载现有教程数据
    if (isEditMode && editId) {
      loadTutorialData(editId)
    }
  }, [isEditMode, editId])

  const loadTutorialData = async (tutorialId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/tutorials/${tutorialId}`)
      if (response.ok) {
        const tutorialData = await response.json()
        const data = tutorialData.data || tutorialData
        
        setTutorial({
          title: data.title || "",
          description: data.description || "",
          content: data.content || "",
          category_id: data.category_id?.toString() || "",
          tags: data.tags || [],
          price: data.price?.toString() || "",
          created_date: data.created_at ? new Date(data.created_at).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          status: data.status || "draft",
        })
        
        toast({
          title: "教程数据加载成功",
          description: `正在编辑：${data.title}`,
        })
      } else {
        toast({
          title: "加载失败",
          description: "无法加载教程数据，请检查教程ID",
          variant: "destructive",
        })
        router.push('/admin')
      }
    } catch (error) {
      toast({
        title: "加载错误",
        description: "请检查网络连接",
        variant: "destructive",
      })
      router.push('/admin')
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch("/api/admin/categories")
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      } else {
        // 设置示例分类数据
        setCategories([
          { id: 1, name: "编程开发", description: "编程相关的教程内容" },
          { id: 2, name: "设计创意", description: "设计和创意相关的教程" },
          { id: 3, name: "商业营销", description: "商业和营销策略教程" },
          { id: 4, name: "生活技能", description: "日常生活技能教程" },
        ])
      }
    } catch (error) {
      console.error("Failed to load categories:", error)
      setCategories([
        { id: 1, name: "编程开发", description: "编程相关的教程内容" },
        { id: 2, name: "设计创意", description: "设计和创意相关的教程" },
        { id: 3, name: "商业营销", description: "商业和营销策略教程" },
        { id: 4, name: "生活技能", description: "日常生活技能教程" },
      ])
    }
  }

  const addTag = () => {
    if (newTag.trim() && !tutorial.tags.includes(newTag.trim())) {
      setTutorial({
        ...tutorial,
        tags: [...tutorial.tags, newTag.trim()],
      })
      setNewTag("")
    }
  }

  const addPresetTag = (tag: string) => {
    if (!tutorial.tags.includes(tag)) {
      setTutorial({
        ...tutorial,
        tags: [...tutorial.tags, tag],
      })
    }
  }

  const addCustomTag = () => {
    if (customTagInput.trim() && !tutorial.tags.includes(customTagInput.trim())) {
      setTutorial({
        ...tutorial,
        tags: [...tutorial.tags, customTagInput.trim()],
      })
      setCustomTagInput("")
      setShowTagDialog(false)
      toast({
        title: "标签添加成功",
        description: `已添加标签：${customTagInput.trim()}`,
      })
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTutorial({
      ...tutorial,
      tags: tutorial.tags.filter((tag) => tag !== tagToRemove),
    })
  }

  const createCategory = async () => {
    if (!newCategory.name.trim()) {
      toast({ title: "请输入分类名称", variant: "destructive" })
      return
    }

    try {
      // 模拟创建分类API调用
      const mockCategory = {
        id: categories.length + 1,
        name: newCategory.name,
        description: newCategory.description,
      }

      setCategories([...categories, mockCategory])
      setTutorial({ ...tutorial, category_id: mockCategory.id.toString() })
      setNewCategory({ name: "", description: "" })
      setShowCategoryDialog(false)

      toast({
        title: "分类创建成功",
        description: `已创建分类：${mockCategory.name}`,
      })
    } catch (error) {
      toast({ title: "创建失败", description: "请稍后重试", variant: "destructive" })
    }
  }

  const handleSave = async (status: "draft" | "published") => {
    if (!tutorial.title.trim()) {
      toast({ title: "请输入教程标题", variant: "destructive" })
      return
    }

    if (!tutorial.description.trim()) {
      toast({ title: "请输入教程描述", variant: "destructive" })
      return
    }

    if (!tutorial.category_id) {
      toast({ title: "请选择教程分类", variant: "destructive" })
      return
    }

    if (!tutorial.content.trim()) {
      toast({ title: "请输入教程内容", variant: "destructive" })
      return
    }

    setLoading(true)

    try {
      // 根据模式选择不同的API端点和方法
      const url = isEditMode ? `/api/admin/tutorials/${editId}` : "/api/admin/tutorials"
      const method = isEditMode ? "PUT" : "POST"
      
      const response = await fetch(url, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: tutorial.title,
          description: tutorial.description,
          content: tutorial.content,
          category_id: tutorial.category_id,
          tags: tutorial.tags,
          price: Number.parseFloat(tutorial.price) || 0,
          created_date: tutorial.created_date,
          status: status,
        }),
      })

      if (response.ok) {
        const result = await response.json()
        const actionText = isEditMode ? "更新" : (status === "draft" ? "保存" : "发布")
        toast({
          title: `教程${actionText}成功`,
          description: isEditMode 
            ? `教程 "${tutorial.title}" 已更新` 
            : (status === "draft" ? "教程已保存为草稿" : "教程已成功发布"),
        })
        
        // 编辑模式下保存成功后重新加载教程数据以确保数据同步
        if (isEditMode && editId) {
          await loadTutorialData(editId)
        } else {
          // 新建模式下返回管理页面
          router.push("/admin")
        }
      } else {
        const errorData = await response.json()
        toast({ 
          title: `${isEditMode ? "更新" : "保存"}失败`, 
          description: errorData.message || "请检查输入信息", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      toast({ 
        title: `${isEditMode ? "更新" : "保存"}错误`, 
        description: "请检查网络连接", 
        variant: "destructive" 
      })
    } finally {
      setLoading(false)
    }
  }

  const getCategoryName = (categoryId: string) => {
    const category = categories.find((c) => c.id.toString() === categoryId)
    return category ? category.name : ""
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" onClick={() => router.push("/admin")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回管理后台
              </Button>
              <div>
                <h1 className="text-2xl font-bold">{isEditMode ? "编辑教程" : "创建新教程"}</h1>
                <p className="text-gray-600">填写教程信息并发布</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" asChild>
                <a href="/">
                  <BookOpen className="h-4 w-4 mr-2" />
                  返回商城
                </a>
              </Button>
              <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
                <Eye className="h-4 w-4 mr-2" />
                {previewMode ? "编辑模式" : "预览模式"}
              </Button>
              <Button variant="outline" onClick={() => handleSave("draft")} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {isEditMode ? "保存更新" : "保存草稿"}
              </Button>
              <Button onClick={() => handleSave("published")} disabled={loading}>
                {isEditMode ? "更新并发布" : "发布教程"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {!previewMode ? (
            // 编辑模式
            <div className="space-y-6">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>填写教程的基本信息</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="title">教程标题 *</Label>
                    <Input
                      id="title"
                      placeholder="输入教程标题"
                      value={tutorial.title}
                      onChange={(e) => setTutorial({ ...tutorial, title: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">教程描述 *</Label>
                    <Textarea
                      id="description"
                      placeholder="详细描述教程内容、适用人群、学习目标等"
                      rows={4}
                      value={tutorial.description}
                      onChange={(e) => setTutorial({ ...tutorial, description: e.target.value })}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="category">教程分类 *</Label>
                      <div className="flex space-x-2">
                        <Select
                          value={tutorial.category_id}
                          onValueChange={(value) => setTutorial({ ...tutorial, category_id: value })}
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="选择分类" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id.toString()}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Dialog open={showCategoryDialog} onOpenChange={setShowCategoryDialog}>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="icon">
                              <Plus className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>创建新分类</DialogTitle>
                              <DialogDescription>添加一个新的教程分类</DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="category-name">分类名称 *</Label>
                                <Input
                                  id="category-name"
                                  placeholder="输入分类名称"
                                  value={newCategory.name}
                                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                                />
                              </div>
                              <div>
                                <Label htmlFor="category-desc">分类描述</Label>
                                <Textarea
                                  id="category-desc"
                                  placeholder="描述这个分类的用途"
                                  value={newCategory.description}
                                  onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                                />
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  onClick={() => setShowCategoryDialog(false)}
                                  className="flex-1"
                                >
                                  取消
                                </Button>
                                <Button onClick={createCategory} className="flex-1">
                                  创建分类
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="price">价格（元）</Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={tutorial.price}
                        onChange={(e) => setTutorial({ ...tutorial, price: e.target.value })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="created-date">创建日期</Label>
                      <Input
                        id="created-date"
                        type="date"
                        value={tutorial.created_date}
                        onChange={(e) => setTutorial({ ...tutorial, created_date: e.target.value })}
                      />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label>标签</Label>
                      <Dialog open={showTagDialog} onOpenChange={setShowTagDialog}>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4 mr-2" />
                            管理标签
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>标签管理</DialogTitle>
                            <DialogDescription>选择预设标签或创建自定义标签</DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            {/* 预设标签 */}
                            <div>
                              <Label className="text-sm font-medium">预设标签</Label>
                              <div className="flex flex-wrap gap-2 mt-2 max-h-32 overflow-y-auto">
                                {presetTags.map((tag, index) => (
                                  <Button
                                    key={index}
                                    variant={tutorial.tags.includes(tag) ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => addPresetTag(tag)}
                                    disabled={tutorial.tags.includes(tag)}
                                  >
                                    {tag}
                                    {tutorial.tags.includes(tag) && <X className="h-3 w-3 ml-1" />}
                                  </Button>
                                ))}
                              </div>
                            </div>

                            {/* 自定义标签 */}
                            <div>
                              <Label className="text-sm font-medium">创建自定义标签</Label>
                              <div className="flex space-x-2 mt-2">
                                <Input
                                  placeholder="输入自定义标签"
                                  value={customTagInput}
                                  onChange={(e) => setCustomTagInput(e.target.value)}
                                  onKeyPress={(e) => e.key === "Enter" && addCustomTag()}
                                />
                                <Button onClick={addCustomTag}>
                                  <Plus className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {/* 已选标签 */}
                            {tutorial.tags.length > 0 && (
                              <div>
                                <Label className="text-sm font-medium">已选标签</Label>
                                <div className="flex flex-wrap gap-2 mt-2">
                                  {tutorial.tags.map((tag, index) => (
                                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                      {tag}
                                      <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-2">
                      {tutorial.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                        </Badge>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      <Input
                        placeholder="快速添加标签"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addTag()}
                      />
                      <Button type="button" variant="outline" onClick={addTag}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 教程内容 */}
              <Card>
                <CardHeader>
                  <CardTitle>教程内容</CardTitle>
                  <CardDescription>使用富文本编辑器编写教程内容</CardDescription>
                </CardHeader>
                <CardContent>
                  <HTMLTutorialEditor
                    initialContent={tutorial.content}
                    onAutoSave={(content) => {
                      setTutorial({ ...tutorial, content })
                    }}
                    onSave={(content) => {
                      setTutorial({ ...tutorial, content })
                      toast({ title: "内容已保存", description: "教程内容已自动保存" })
                    }}
                    placeholder="在这里编写教程内容，支持可视化编辑和HTML源码..."
                    className="min-h-[500px]"
                  />
                  <div className="mt-4 text-sm text-gray-500">
                    <p>✨ 支持多种编辑模式：可视化编辑、HTML源码、实时预览</p>
                    <p>🎯 支持章节结构：data-section属性、检查点、互动练习等</p>
                    <p>💡 快捷键：Ctrl+S 保存，支持HTML标签直接渲染</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            // 预览模式
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-2xl mb-3">{tutorial.title || "教程标题"}</CardTitle>
                      <CardDescription className="text-base mb-4">{tutorial.description || "教程描述"}</CardDescription>

                      <div className="flex items-center space-x-4 mb-4">
                        <Badge variant="secondary">{getCategoryName(tutorial.category_id) || "未选择分类"}</Badge>
                        <Badge variant="outline">预览模式</Badge>
                      </div>

                      {tutorial.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {tutorial.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(tutorial.created_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-blue-600">¥{tutorial.price || "0.00"}</div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>教程内容</CardTitle>
                </CardHeader>
                <CardContent>
                  <ErrorBoundary
                    onError={(error) => {
                      console.error('管理后台预览内容渲染错误:', error)
                      toast({
                        title: "内容预览错误",
                        description: "教程内容格式可能有问题，请检查HTML格式",
                        variant: "destructive"
                      })
                    }}
                  >
                    {tutorial.content ? (
                      <div
                        className="prose max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900"
                        dangerouslySetInnerHTML={{ __html: tutorial.content }}
                      />
                    ) : (
                      <p className="text-gray-500 italic">暂无内容</p>
                    )}
                  </ErrorBoundary>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
