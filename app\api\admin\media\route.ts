import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({ error: "没有上传文件" }, { status: 400 })
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: "不支持的文件类型" }, { status: 400 })
    }

    // 验证文件大小 (5MB 限制)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: "文件大小超过5MB限制" }, { status: 400 })
    }

    // 生成唯一文件名
    const timestamp = Date.now()
    const fileExt = file.name.split('.').pop()
    const fileName = `tutorial-media/${timestamp}-${Math.random().toString(36).substring(2)}.${fileExt}`

    // 上传到Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseAdmin
      .storage
      .from('tutorial-media')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error("Storage upload error:", uploadError)
      return NextResponse.json({ error: "文件上传失败" }, { status: 500 })
    }

    // 获取公开URL
    const { data: urlData } = supabaseAdmin
      .storage
      .from('tutorial-media')
      .getPublicUrl(fileName)

    // 保存文件记录到数据库
    const { data: mediaRecord, error: dbError } = await supabaseAdmin
      .from('tutorial_media')
      .insert({
        file_name: file.name,
        file_path: fileName,
        file_type: file.type,
        file_size: file.size,
        public_url: urlData.publicUrl,
        uploaded_at: new Date().toISOString()
      })
      .select()
      .single()

    if (dbError) {
      console.error("Database error:", dbError)
      // 如果数据库插入失败，删除已上传的文件
      await supabaseAdmin.storage
        .from('tutorial-media')
        .remove([fileName])
      return NextResponse.json({ error: "文件记录保存失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: mediaRecord.id,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        url: urlData.publicUrl,
        path: fileName
      }
    })

  } catch (error) {
    console.error("Media upload error:", error)
    return NextResponse.json({ error: "上传过程中发生错误" }, { status: 500 })
  }
}

export async function GET() {
  try {
    const { data: mediaFiles, error } = await supabaseAdmin
      .from('tutorial_media')
      .select('*')
      .order('uploaded_at', { ascending: false })

    if (error) {
      console.error("Database error:", error)
      return NextResponse.json({ error: "获取媒体文件失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: mediaFiles
    })

  } catch (error) {
    console.error("Get media files error:", error)
    return NextResponse.json({ error: "获取媒体文件失败" }, { status: 500 })
  }
}