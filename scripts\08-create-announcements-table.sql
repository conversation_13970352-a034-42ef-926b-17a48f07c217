-- 创建公告表
CREATE TABLE IF NOT EXISTS announcements (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
    priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 100),
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    target_audience VARCHAR(50) DEFAULT 'all' CHECK (target_audience IN ('all', 'users', 'admins')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VA<PERSON>HA<PERSON>(100) DEFAULT 'admin'
);

-- 创建公告已读记录表
CREATE TABLE IF NOT EXISTS announcement_reads (
    id SERIAL PRIMARY KEY,
    announcement_id INTEGER REFERENCES announcements(id) ON DELETE CASCADE,
    user_identifier VARCHAR(255) NOT NULL,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(announcement_id, user_identifier)
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_announcements_active_priority ON announcements(is_active, priority DESC, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_announcements_date_range ON announcements(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_announcement_reads_user ON announcement_reads(user_identifier, read_at DESC);

-- 插入示例公告数据
INSERT INTO announcements (title, content, type, priority, target_audience) VALUES
('欢迎使用知识商城', '感谢您使用我们的在线教程平台！这里有丰富的学习资源等待您探索。', 'success', 80, 'all'),
('系统维护通知', '本系统将于本周六凌晨2:00-4:00进行例行维护，期间可能无法正常访问，请您提前安排学习时间。', 'warning', 90, 'all'),
('新功能发布', '新增学习进度追踪功能，您现在可以查看详细的学习统计和进度报告。', 'info', 70, 'users');

COMMENT ON TABLE announcements IS '系统公告表';
COMMENT ON TABLE announcement_reads IS '用户公告已读记录表';

COMMENT ON COLUMN announcements.type IS '公告类型：info-信息，warning-警告，success-成功，error-错误';
COMMENT ON COLUMN announcements.priority IS '优先级：0-100，数字越大优先级越高';
COMMENT ON COLUMN announcements.target_audience IS '目标受众：all-所有用户，users-普通用户，admins-管理员';