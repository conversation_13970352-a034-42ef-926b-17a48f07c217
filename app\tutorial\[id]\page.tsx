"use client"

import { useState, useEffect, useRef } from "react"
import { useParams, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, BookOpen, Lock, Unlock, Clock, Tag, Trophy, Target, MapPin } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { TableOfContents } from "@/components/learning/TableOfContents"
import { 
  parseContentSections,
  calculateProgressPercentage,
  scrollToSection,
  setupSectionObserver,
  formatLearningTime,
  LearningProgressStore,
  type ChapterSection,
  type LearningProgress
} from "@/lib/learning-utils"

interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_name: string
  tags: string[]
  price: number
  status: string
  created_at: string
}

export default function TutorialPage() {
  const params = useParams()
  const router = useRouter()
  const [tutorial, setTutorial] = useState<Tutorial | null>(null)
  const [isUnlocked, setIsUnlocked] = useState(false)
  const [loading, setLoading] = useState(true)
  const [sections, setSections] = useState<ChapterSection[]>([])
  const [currentSection, setCurrentSection] = useState<string>('')
  const [learningProgress, setLearningProgress] = useState<LearningProgress | null>(null)
  const [startTime, setStartTime] = useState<number>(0)
  const [isLearning, setIsLearning] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (params.id) {
      loadTutorial(params.id as string)
    }
  }, [params.id])

  // 解析内容章节并设置观察器
  useEffect(() => {
    if (tutorial && isUnlocked) {
      const parsedSections = parseContentSections(tutorial.content)
      setSections(parsedSections)
      
      // 加载本地学习进度
      const savedProgress = LearningProgressStore.load(tutorial.id)
      setLearningProgress(savedProgress)
      
      // 更新章节完成状态
      const updatedSections = parsedSections.map(section => ({
        ...section,
        completed: savedProgress.completedSections.includes(section.id),
        currentlyViewing: section.id === savedProgress.currentSection,
        subsections: section.subsections?.map(sub => ({
          ...sub,
          completed: savedProgress.completedSections.includes(sub.id),
          currentlyViewing: sub.id === savedProgress.currentSection
        }))
      }))
      setSections(updatedSections)
      setCurrentSection(savedProgress.currentSection)
    }
  }, [tutorial, isUnlocked])

  // 设置章节观察器
  useEffect(() => {
    if (sections.length > 0 && isUnlocked) {
      // 清理之前的观察器
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      
      // 延迟设置观察器，等待DOM更新
      setTimeout(() => {
        observerRef.current = setupSectionObserver(
          sections,
          handleSectionChange,
          0.3
        )
      }, 1000)
    }
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [sections, isUnlocked])

  const handleSectionChange = (sectionId: string) => {
    if (sectionId !== currentSection) {
      setCurrentSection(sectionId)
      
      // 更新章节状态
      setSections(prev => prev.map(section => ({
        ...section,
        currentlyViewing: section.id === sectionId,
        subsections: section.subsections?.map(sub => ({
          ...sub,
          currentlyViewing: sub.id === sectionId
        }))
      })))
      
      // 保存当前位置
      if (learningProgress) {
        const updated = { ...learningProgress, currentSection: sectionId }
        setLearningProgress(updated)
        LearningProgressStore.save(tutorial!.id, { currentSection: sectionId })
      }
    }
  }

  const handleSectionClick = (sectionId: string) => {
    scrollToSection(sectionId, 120)
    setCurrentSection(sectionId)
  }

  const markSectionComplete = (sectionId: string) => {
    if (!learningProgress || !tutorial) return
    
    const completedSections = [...learningProgress.completedSections]
    if (!completedSections.includes(sectionId)) {
      completedSections.push(sectionId)
      
      const progressPercentage = calculateProgressPercentage(sections, completedSections)
      const updated = {
        ...learningProgress,
        completedSections,
        progressPercentage,
        lastAccessed: new Date().toISOString()
      }
      
      setLearningProgress(updated)
      LearningProgressStore.save(tutorial.id, updated)
      
      // 更新章节状态
      setSections(prev => prev.map(section => ({
        ...section,
        completed: completedSections.includes(section.id),
        subsections: section.subsections?.map(sub => ({
          ...sub,
          completed: completedSections.includes(sub.id)
        }))
      })))
      
      // 发送到服务器
      updateServerProgress(updated)
      
      toast({
        title: "章节已完成",
        description: `进度更新：${progressPercentage}%`
      })
    }
  }

  const startLearning = () => {
    setStartTime(Date.now())
    setIsLearning(true)
    
    if (learningProgress && tutorial) {
      const updated = {
        ...learningProgress,
        lastAccessed: new Date().toISOString()
      }
      setLearningProgress(updated)
      LearningProgressStore.save(tutorial.id, updated)
    }
    
    toast({
      title: "开始学习",
      description: "学习时间开始计算"
    })
  }

  const pauseLearning = () => {
    if (isLearning && startTime > 0 && learningProgress && tutorial) {
      const sessionTime = Math.floor((Date.now() - startTime) / 1000 / 60)
      const totalTime = learningProgress.totalTimeSpent + sessionTime
      
      setIsLearning(false)
      setStartTime(0)
      
      const updated = {
        ...learningProgress,
        totalTimeSpent: totalTime,
        lastAccessed: new Date().toISOString()
      }
      
      setLearningProgress(updated)
      LearningProgressStore.save(tutorial.id, updated)
      updateServerProgress(updated)
      
      toast({
        title: "学习暂停",
        description: `本次学习：${sessionTime}分钟`
      })
    }
  }

  const updateServerProgress = async (progressData: LearningProgress) => {
    if (!tutorial) return
    
    try {
      await fetch('/api/learning/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tutorialId: tutorial.id,
          status: progressData.progressPercentage === 100 ? 'completed' : 'in_progress',
          progressPercentage: progressData.progressPercentage,
          timeSpent: progressData.totalTimeSpent,
          completedSections: progressData.completedSections,
          currentSection: progressData.currentSection
        })
      })
    } catch (error) {
      console.warn('Failed to sync progress to server:', error)
    }
  }

  const loadTutorial = async (id: string) => {
    try {
      const response = await fetch(`/api/tutorial/${id}`)

      if (response.ok) {
        const data = await response.json()
        setTutorial(data.tutorial)
        setIsUnlocked(data.is_unlocked)
      } else if (response.status === 404) {
        toast({
          title: "教程不存在",
          description: "请检查链接是否正确",
          variant: "destructive",
        })
        router.push("/")
      } else if (response.status === 403) {
        toast({
          title: "需要解锁",
          description: "请使用有效密钥解锁此教程",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "加载失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!tutorial) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="text-center py-8">
            <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">教程不存在</h2>
            <p className="text-gray-600 mb-4">请检查链接是否正确</p>
            <Button onClick={() => router.push("/")}>返回首页</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const totalEstimatedTime = sections.reduce((total, section) => 
    total + section.estimatedTime + (section.subsections?.reduce((subTotal, sub) => subTotal + sub.estimatedTime, 0) || 0), 0
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h1 className="text-xl font-semibold">{tutorial.title}</h1>
                {tutorial.status === 'archived' && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
                    已下架
                  </Badge>
                )}
              </div>
              <p className="text-gray-600 text-sm">{tutorial.category_name}</p>
            </div>
            {isUnlocked && learningProgress && (
              <div className="flex items-center space-x-4">
                <Badge variant="outline">
                  进度：{learningProgress.progressPercentage}%
                </Badge>
                {isLearning ? (
                  <Button variant="outline" onClick={pauseLearning} size="sm">
                    暂停学习
                  </Button>
                ) : (
                  <Button onClick={startLearning} size="sm">
                    {learningProgress.totalTimeSpent > 0 ? '继续学习' : '开始学习'}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {isUnlocked ? (
          <div className="flex gap-6">
            {/* 左侧目录 */}
            <div className="w-80 flex-shrink-0">
              <TableOfContents
                chapters={sections}
                currentSection={currentSection}
                completedSections={learningProgress?.completedSections || []}
                totalProgress={learningProgress?.progressPercentage || 0}
                totalTimeSpent={learningProgress?.totalTimeSpent || 0}
                onSectionClick={handleSectionClick}
                onMarkComplete={markSectionComplete}
              />
            </div>

            {/* 右侧内容 */}
            <div className="flex-1 max-w-4xl">
              {/* 教程总结栏目 */}
              <Card className="mb-6">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-2xl mb-3 flex items-center">
                        <BookOpen className="h-6 w-6 mr-3" />
                        {tutorial.title}
                        {tutorial.status === 'archived' && (
                          <Badge variant="outline" className="ml-3 bg-orange-50 text-orange-600 border-orange-200">
                            已下架
                          </Badge>
                        )}
                      </CardTitle>
                      {tutorial.status === 'archived' && (
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                          <p className="text-orange-700 text-sm">
                            <span className="font-medium">注意：</span>
                            此教程已下架，不再对外销售。但您已经解锁，可以继续学习内容。
                          </p>
                        </div>
                      )}
                      <CardDescription className="text-base mb-4">
                        {tutorial.description}
                      </CardDescription>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <span className="text-sm">
                            预计时长：{formatLearningTime(totalEstimatedTime)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Target className="h-4 w-4 text-green-600" />
                          <span className="text-sm">
                            {sections.length} 个章节
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Trophy className="h-4 w-4 text-orange-600" />
                          <span className="text-sm">
                            互动学习
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 mb-4">
                        <Badge variant="secondary">{tutorial.category_name}</Badge>
                        <Badge className="bg-green-100 text-green-800">
                          <Unlock className="h-3 w-3 mr-1" />
                          已解锁
                        </Badge>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {tutorial.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      {learningProgress && (
                        <div className="space-y-3 pt-4 border-t">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">学习进度</span>
                            <span className="text-sm text-gray-500">
                              {learningProgress.progressPercentage}%
                            </span>
                          </div>
                          <Progress value={learningProgress.progressPercentage} className="h-2" />
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>
                              已学习：{formatLearningTime(learningProgress.totalTimeSpent)}
                            </span>
                            <span>
                              {learningProgress.completedSections.length} / {sections.reduce((count, s) => count + 1 + (s.subsections?.length || 0), 0)} 完成
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* 教程内容 */}
              <Card>
                <CardContent className="p-6">
                  <ErrorBoundary
                    onError={(error) => {
                      console.error('教程内容渲染错误:', error)
                      toast({
                        title: "内容渲染错误",
                        description: "教程内容格式可能有问题，请联系管理员",
                        variant: "destructive"
                      })
                    }}
                  >
                    {tutorial.content ? (
                      <div
                        className="tutorial-content prose max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100"
                        style={{
                          lineHeight: '1.6'
                        }}
                        dangerouslySetInnerHTML={{ __html: tutorial.content }}
                      />
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <p>该教程暂无内容</p>
                      </div>
                    )}
                  </ErrorBoundary>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <Card className="border-2 border-dashed border-gray-300 max-w-2xl mx-auto">
            <CardContent className="text-center py-12">
              <Lock className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold mb-2">内容已锁定</h3>
              <p className="text-gray-600 mb-6">请使用有效的验证密钥解锁此教程内容</p>
              <Button onClick={() => router.push("/")}>前往解锁</Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
