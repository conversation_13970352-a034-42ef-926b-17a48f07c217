-- ==========================================
-- Supabase Storage配置脚本
-- 创建媒体文件存储桶和安全策略
-- ==========================================

-- 创建教程媒体存储桶
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('tutorial-media', 'tutorial-media', true, 52428800, ARRAY[
    'image/jpeg',
    'image/png', 
    'image/webp',
    'image/gif',
    'video/mp4',
    'video/webm',
    'application/pdf',
    'text/plain',
    'text/markdown'
  ])
ON CONFLICT (id) DO NOTHING;

-- 存储桶安全策略

-- 1. 允许认证用户上传文件
CREATE POLICY "Allow authenticated users to upload tutorial media" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'tutorial-media' AND auth.role() = 'authenticated');

-- 2. 允许所有人查看公开文件
CREATE POLICY "Allow public access to tutorial media" ON storage.objects
  FOR SELECT USING (bucket_id = 'tutorial-media');

-- 3. 允许文件所有者删除文件
CREATE POLICY "Allow users to delete their own tutorial media" ON storage.objects
  FOR DELETE USING (bucket_id = 'tutorial-media' AND auth.uid()::text = (storage.foldername(name))[1]);

-- 创建媒体处理函数
CREATE OR REPLACE FUNCTION process_media_upload()
RETURNS TRIGGER AS $$
DECLARE
  file_info record;
  tutorial_id_from_path integer;
BEGIN
  -- 从存储路径中提取tutorial_id
  SELECT (string_to_array(NEW.name, '/'))[2]::integer INTO tutorial_id_from_path;
  
  -- 插入媒体记录到tutorial_media表
  INSERT INTO tutorial_media (
    tutorial_id,
    file_name,
    original_name,
    file_type,
    mime_type,
    file_size,
    storage_path,
    public_url,
    uploaded_by
  ) VALUES (
    tutorial_id_from_path,
    (string_to_array(NEW.name, '/'))[3], -- 文件名
    COALESCE(NEW.metadata->>'originalName', (string_to_array(NEW.name, '/'))[3]),
    CASE 
      WHEN NEW.metadata->>'mimetype' LIKE 'image/%' THEN 'image'
      WHEN NEW.metadata->>'mimetype' LIKE 'video/%' THEN 'video'
      WHEN NEW.metadata->>'mimetype' LIKE 'application/pdf' THEN 'document'
      ELSE 'other'
    END,
    NEW.metadata->>'mimetype',
    NEW.metadata->>'size'::bigint,
    NEW.name,
    CONCAT('https://tvkilvgjswuhigfkhyhf.supabase.co/storage/v1/object/public/', NEW.bucket_id, '/', NEW.name),
    NEW.owner::text
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器监听文件上传
CREATE TRIGGER on_media_upload
  AFTER INSERT ON storage.objects
  FOR EACH ROW
  WHEN (NEW.bucket_id = 'tutorial-media')
  EXECUTE FUNCTION process_media_upload();

-- 查看存储桶配置
SELECT 
  id,
  name,
  public,
  file_size_limit / 1024 / 1024 as max_size_mb,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE id = 'tutorial-media';

-- 查看安全策略
SELECT 
  policyname,
  cmd,
  permissive,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects';

COMMENT ON FUNCTION process_media_upload() IS '处理媒体文件上传，自动在tutorial_media表中创建记录';
COMMENT ON TRIGGER on_media_upload ON storage.objects IS '监听tutorial-media桶的文件上传事件';

-- 创建清理函数（删除未引用的媒体文件）
CREATE OR REPLACE FUNCTION cleanup_unused_media()
RETURNS integer AS $$
DECLARE
  deleted_count integer := 0;
  media_record record;
BEGIN
  -- 查找超过30天未被引用的媒体文件
  FOR media_record IN 
    SELECT id, storage_path 
    FROM tutorial_media 
    WHERE usage_count = 0 
    AND created_at < NOW() - INTERVAL '30 days'
  LOOP
    -- 从存储中删除文件
    DELETE FROM storage.objects 
    WHERE bucket_id = 'tutorial-media' 
    AND name = media_record.storage_path;
    
    -- 从数据库中删除记录
    DELETE FROM tutorial_media WHERE id = media_record.id;
    
    deleted_count := deleted_count + 1;
  END LOOP;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION cleanup_unused_media() IS '清理30天内未使用的媒体文件';