/**
 * 真实内容测试
 * 测试ui-ux-design-system-guide.html的解析效果
 */

const fs = require('fs');
const path = require('path');

// 导入之前的解析函数
const { parseContentSections } = require('./format-compatibility.js');

function testRealContent() {
  console.log('🧪 测试真实教程内容解析...\n');
  
  try {
    // 读取真实的教程文件
    const filePath = path.join(__dirname, '..', 'content', 'ui-ux-design-system-guide.html');
    const htmlContent = fs.readFileSync(filePath, 'utf8');
    
    console.log('📄 解析 ui-ux-design-system-guide.html');
    const sections = parseContentSections(htmlContent);
    
    console.log(`✅ 成功解析出 ${sections.length} 个章节:\n`);
    
    let totalTime = 0;
    sections.forEach((section, index) => {
      console.log(`${index + 1}. ${section.id}: ${section.title}`);
      console.log(`   ⏱️  预计时间: ${section.estimatedTime} 分钟`);
      console.log(`   🏷️  类型: ${section.type}`);
      console.log('');
      totalTime += section.estimatedTime;
    });
    
    console.log(`📊 总学习时间: ${totalTime} 分钟 (${Math.round(totalTime/60*10)/10} 小时)`);
    
    // 测试进度计算
    console.log('\n📈 进度计算测试:');
    
    function calculateProgressPercentage(sections, completedSections) {
      if (!sections.length) return 0;
      
      const totalWeight = sections.reduce((sum, section) => sum + section.estimatedTime, 0);
      const completedWeight = sections
        .filter(section => completedSections.includes(section.id))
        .reduce((sum, section) => sum + section.estimatedTime, 0);
      
      return Math.round((completedWeight / totalWeight) * 100);
    }
    
    // 模拟不同的完成情况
    const testCases = [
      { completed: ['intro'], name: '只完成介绍' },
      { completed: ['intro', '1'], name: '完成介绍和第一章' },
      { completed: ['intro', '1', '2'], name: '完成前三章' },
      { completed: ['intro', '1', '2', '3', '4'], name: '完成大部分章节' },
      { completed: sections.map(s => s.id), name: '完成全部章节' }
    ];
    
    testCases.forEach(testCase => {
      const progress = calculateProgressPercentage(sections, testCase.completed);
      console.log(`   ${testCase.name}: ${progress}%`);
    });
    
    // 检查章节结构
    console.log('\n🔍 章节结构分析:');
    const typeCount = sections.reduce((acc, section) => {
      acc[section.type] = (acc[section.type] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(typeCount).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} 个`);
    });
    
    // 验证必要属性
    console.log('\n✅ 属性验证:');
    let hasErrors = false;
    
    sections.forEach(section => {
      if (!section.id) {
        console.log(`❌ 章节缺少ID: ${section.title}`);
        hasErrors = true;
      }
      if (!section.title) {
        console.log(`❌ 章节缺少标题: ${section.id}`);
        hasErrors = true;
      }
      if (section.estimatedTime <= 0) {
        console.log(`❌ 章节时间无效: ${section.id} - ${section.estimatedTime}`);
        hasErrors = true;
      }
    });
    
    if (!hasErrors) {
      console.log('   所有章节属性验证通过 ✅');
    }
    
    return {
      sectionsCount: sections.length,
      totalTime: totalTime,
      types: typeCount,
      hasErrors: hasErrors
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return null;
  }
}

// 如果直接运行
if (require.main === module) {
  const result = testRealContent();
  if (result) {
    console.log('\n🎉 真实内容测试完成！');
    console.log(`📋 解析了 ${result.sectionsCount} 个章节，总时长 ${result.totalTime} 分钟`);
  }
}

module.exports = { testRealContent };