/**
 * 前端缓存刷新Hook - 智能检测和处理数据更新
 * 解决管理员添加教程后首页不实时更新的问题
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { clientCacheUtils } from '@/lib/cache-manager'

interface SmartRefreshOptions {
  url: string
  interval?: number // 轮询间隔（毫秒）
  maxRetries?: number
  onDataChange?: (newData: any, oldData: any) => void
  onError?: (error: Error) => void
  enabled?: boolean // 是否启用智能刷新
}

interface SmartRefreshResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  lastUpdate: Date | null
  forceRefresh: () => Promise<void>
  refreshCount: number
  cacheStatus: 'fresh' | 'stale' | 'updating' | 'error'
}

/**
 * 智能数据刷新Hook
 * 
 * 特性：
 * - 自动检测数据变化
 * - 智能轮询策略
 * - 缓存失效处理
 * - 错误重试机制
 * - 性能优化
 */
export function useSmartRefresh<T = any>(
  options: SmartRefreshOptions
): SmartRefreshResult<T> {
  
  const {
    url,
    interval = 30000, // 默认30秒轮询
    maxRetries = 3,
    onDataChange,
    onError,
    enabled = true
  } = options

  // 状态管理
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [refreshCount, setRefreshCount] = useState(0)
  const [cacheStatus, setCacheStatus] = useState<'fresh' | 'stale' | 'updating' | 'error'>('fresh')

  // Refs for cleanup and state persistence
  const intervalRef = useRef<NodeJS.Timeout>()
  const abortControllerRef = useRef<AbortController>()
  const lastDataRef = useRef<T | null>(null)
  const retryCountRef = useRef(0)
  const isInitializedRef = useRef(false)
  const lastFetchTimeRef = useRef(0)

  /**
   * 数据获取函数 - 带智能缓存处理
   */
  const fetchData = useCallback(async (force = false) => {
    if (!enabled) return
    
    // 🔧 防抖机制 - 避免频繁请求
    const now = Date.now()
    if (!force && now - lastFetchTimeRef.current < 1000) {
      console.log('⏳ 请求过于频繁，跳过')
      return
    }
    lastFetchTimeRef.current = now
    
    try {
      setLoading(true)
      setCacheStatus('updating')
      setError(null)

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 创建新的AbortController
      abortControllerRef.current = new AbortController()

      // 构建请求URL - 强制刷新时绕过缓存
      const requestUrl = force ? clientCacheUtils.forceRefresh(url) : url
      
      console.log(`🔄 获取数据: ${requestUrl} (force: ${force})`)

      // 发送请求
      const response = await fetch(requestUrl, {
        signal: abortControllerRef.current.signal,
        cache: force ? 'no-cache' : 'default',
        headers: {
          'Cache-Control': force ? 'no-cache' : 'max-age=0',
          'X-Requested-With': 'smart-refresh',
          'X-Force-Refresh': force.toString(),
          'X-Refresh-Count': refreshCount.toString()
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const newData = await response.json()
      
      // 检查数据是否真的发生变化
      const hasDataChanged = JSON.stringify(newData) !== JSON.stringify(lastDataRef.current)
      
      if (hasDataChanged || force) {
        // 触发数据变化回调
        if (onDataChange && lastDataRef.current !== null) {
          onDataChange(newData, lastDataRef.current)
        }
        
        setData(newData)
        lastDataRef.current = newData
        setLastUpdate(new Date())
        setRefreshCount(prev => prev + 1)
        
        console.log(`✅ 数据已更新 (变化: ${hasDataChanged}, 强制: ${force})`)
      } else {
        console.log('📊 数据无变化，跳过更新')
      }

      setCacheStatus('fresh')
      retryCountRef.current = 0 // 重置重试计数

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🚫 请求已取消')
        return
      }

      const error = err instanceof Error ? err : new Error('Unknown fetch error')
      console.error('❌ 数据获取失败:', error.message)
      
      setError(error)
      setCacheStatus('error')
      
      // 错误回调
      if (onError) {
        onError(error)
      }

      // 重试逻辑
      retryCountRef.current++
      if (retryCountRef.current < maxRetries) {
        console.log(`🔁 准备重试 (${retryCountRef.current}/${maxRetries})`)
        setTimeout(() => {
          fetchData(force)
        }, Math.pow(2, retryCountRef.current) * 1000) // 指数退避
      }

    } finally {
      setLoading(false)
    }
  }, [url, enabled, maxRetries]) // 🔧 移除引起循环的依赖

  /**
   * 强制刷新函数
   */
  const forceRefresh = useCallback(async () => {
    console.log('🚀 强制刷新数据')
    await fetchData(true)
  }, [fetchData])

  /**
   * 智能轮询管理
   */
  useEffect(() => {
    if (!enabled) return

    console.log('🔄 初始化智能刷新Hook')

    // 🔧 只在首次初始化时加载数据
    if (!isInitializedRef.current) {
      fetchData()
      isInitializedRef.current = true
    }

    // 设置轮询
    if (interval > 0) {
      console.log(`⏰ 设置轮询间隔: ${interval}ms`)
      intervalRef.current = setInterval(() => {
        // 只有在页面可见时才轮询
        if (document.visibilityState === 'visible') {
          fetchData()
        }
      }, interval)
    }

    // 页面可见性变化处理
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('👁️ 页面重新可见，检查是否需要刷新')
        // 页面重新可见时，简单轮询即可，不需要强制刷新
        fetchData()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 清理函数
    return () => {
      console.log('🧹 清理智能刷新Hook')
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, interval]) // 🔧 移除fetchData依赖，避免循环

  /**
   * 网络状态变化处理
   */
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 网络重新连接，刷新数据')
      forceRefresh()
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [forceRefresh])

  return {
    data,
    loading,
    error,
    lastUpdate,
    forceRefresh,
    refreshCount,
    cacheStatus
  }
}

/**
 * 专门用于教程列表的智能刷新Hook
 */
export function useTutorialsSmartRefresh(enabled = true) {
  return useSmartRefresh<{
    success: boolean
    data: any[]
    pagination?: any
  }>({
    url: '/api/public/tutorials',
    interval: 30000, // 30秒检查一次
    maxRetries: 3,
    enabled,
    onDataChange: (newData, oldData) => {
      // 检查是否有新教程
      const newTutorialCount = newData?.data?.length || 0
      const oldTutorialCount = oldData?.data?.length || 0
      
      if (newTutorialCount > oldTutorialCount) {
        console.log(`🎉 发现 ${newTutorialCount - oldTutorialCount} 个新教程!`)
        
        // 可以在这里显示通知
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('新教程发布', {
            body: `有 ${newTutorialCount - oldTutorialCount} 个新教程可以学习了！`,
            icon: '/favicon.ico'
          })
        }
      }
    },
    onError: (error) => {
      console.error('教程数据刷新失败:', error)
    }
  })
}

/**
 * 全局缓存刷新工具
 */
export const globalCacheRefresh = {
  /**
   * 刷新所有教程相关缓存
   */
  refreshTutorials: async () => {
    try {
      console.log('🔄 全局教程缓存刷新开始')
      
      // 清除客户端缓存
      clientCacheUtils.clearFetchCache()
      
      // 强制刷新关键API
      const refreshPromises = [
        clientCacheUtils.smartRefresh('/api/public/tutorials'),
        clientCacheUtils.smartRefresh('/api/public/categories'),
        clientCacheUtils.smartRefresh('/api/user-unlocks')
      ]
      
      await Promise.allSettled(refreshPromises)
      
      console.log('✅ 全局教程缓存刷新完成')
      
      // 刷新页面数据（不刷新整个页面）
      window.dispatchEvent(new CustomEvent('cache-refreshed', {
        detail: { type: 'tutorials', timestamp: Date.now() }
      }))
      
    } catch (error) {
      console.error('❌ 全局缓存刷新失败:', error)
    }
  },

  /**
   * 监听缓存刷新事件
   */
  onCacheRefresh: (callback: (detail: any) => void) => {
    const handler = (event: CustomEvent) => callback(event.detail)
    window.addEventListener('cache-refreshed', handler as EventListener)
    
    return () => {
      window.removeEventListener('cache-refreshed', handler as EventListener)
    }
  }
}