-- 更新密钥表结构以支持导出功能
-- 添加exported状态和导出时间字段

-- 修改status字段的CHECK约束以包含exported状态
ALTER TABLE tutorial_keys DROP CONSTRAINT IF EXISTS tutorial_keys_status_check;
ALTER TABLE tutorial_keys ADD CONSTRAINT tutorial_keys_status_check 
  CHECK (status IN ('unused', 'used', 'expired', 'exported'));

-- 添加导出时间字段
ALTER TABLE tutorial_keys ADD COLUMN IF NOT EXISTS exported_at TIMESTAMP;

-- 创建导出时间索引
CREATE INDEX IF NOT EXISTS idx_keys_exported_at ON tutorial_keys(exported_at);

-- 为key_code字段添加唯一索引（如果还没有的话）
CREATE UNIQUE INDEX IF NOT EXISTS idx_keys_unique_code ON tutorial_keys(key_code);

-- 更新已有的过期密钥状态（如果有的话）
UPDATE tutorial_keys 
SET status = 'expired' 
WHERE expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP AND status = 'unused';

-- 创建视图来方便统计密钥信息
CREATE OR REPLACE VIEW tutorial_key_stats AS
SELECT 
  t.id as tutorial_id,
  t.title as tutorial_title,
  t.status as tutorial_status,
  COUNT(tk.id) as total_keys,
  COUNT(CASE WHEN tk.status = 'unused' THEN 1 END) as unused_keys,
  COUNT(CASE WHEN tk.status = 'used' THEN 1 END) as used_keys,
  COUNT(CASE WHEN tk.status = 'exported' THEN 1 END) as exported_keys,
  COUNT(CASE WHEN tk.status = 'expired' THEN 1 END) as expired_keys
FROM tutorials t
LEFT JOIN tutorial_keys tk ON t.id = tk.tutorial_id
WHERE t.status = 'published'
GROUP BY t.id, t.title, t.status
ORDER BY t.created_at DESC;