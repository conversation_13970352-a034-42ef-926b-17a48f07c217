# 用户学习进度数据模型设计

## 🎯 系统目标

构建完整的用户学习进度跟踪系统，提供个性化学习体验、成就激励和数据驱动的学习优化。

## 📊 数据模型设计

### 核心实体关系

```
用户 (Users)
├── 学习记录 (UserLearningRecords)
├── 进度跟踪 (LearningProgress) 
├── 学习统计 (LearningStats)
├── 成就记录 (UserAchievements)
└── 学习路径 (LearningPaths)

教程 (Tutorials)
├── 章节结构 (TutorialSections)
├── 学习目标 (LearningObjectives)
└── 先决条件 (Prerequisites)
```

### 数据库表结构

```sql
-- ==========================================
-- 用户学习进度系统数据表
-- ==========================================

-- 教程章节表 (扩展现有教程结构)
CREATE TABLE IF NOT EXISTS tutorial_sections (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  parent_section_id INTEGER REFERENCES tutorial_sections(id), -- 支持嵌套章节
  title VARCHAR(255) NOT NULL,
  content TEXT,
  section_type VARCHAR(20) DEFAULT 'content' CHECK (section_type IN ('content', 'exercise', 'quiz', 'video')),
  order_index INTEGER NOT NULL,
  estimated_reading_time INTEGER DEFAULT 0, -- 预估阅读时间(分钟)
  difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
  is_required BOOLEAN DEFAULT true, -- 是否必修
  unlock_conditions JSONB, -- 解锁条件
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户学习记录表
CREATE TABLE IF NOT EXISTS user_learning_records (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  section_id INTEGER REFERENCES tutorial_sections(id) ON DELETE SET NULL,
  
  -- 进度状态
  status VARCHAR(20) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped', 'paused')),
  progress_percentage DECIMAL(5,2) DEFAULT 0 CHECK (progress_percentage BETWEEN 0 AND 100),
  
  -- 时间追踪
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  total_time_spent INTEGER DEFAULT 0, -- 总学习时间(秒)
  active_time_spent INTEGER DEFAULT 0, -- 活跃学习时间(秒)
  
  -- 学习行为
  scroll_percentage DECIMAL(5,2) DEFAULT 0, -- 滚动进度
  interaction_count INTEGER DEFAULT 0, -- 交互次数
  pause_count INTEGER DEFAULT 0, -- 暂停次数
  replay_count INTEGER DEFAULT 0, -- 重播次数
  
  -- 元数据
  device_type VARCHAR(20), -- desktop, mobile, tablet
  learning_session_id VARCHAR(50), -- 学习会话ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- 确保同一用户对同一章节只有一条记录
  UNIQUE(user_identifier, tutorial_id, section_id)
);

-- 用户学习统计表
CREATE TABLE IF NOT EXISTS user_learning_stats (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  
  -- 基础统计
  total_tutorials_unlocked INTEGER DEFAULT 0,
  total_tutorials_started INTEGER DEFAULT 0,
  total_tutorials_completed INTEGER DEFAULT 0,
  total_sections_completed INTEGER DEFAULT 0,
  
  -- 时间统计
  total_learning_time INTEGER DEFAULT 0, -- 总学习时间(秒)
  average_session_time INTEGER DEFAULT 0, -- 平均学习时长(秒)
  learning_streak_days INTEGER DEFAULT 0, -- 连续学习天数
  max_learning_streak INTEGER DEFAULT 0, -- 最长连续学习天数
  
  -- 分类统计
  favorite_category VARCHAR(100), -- 最喜欢的分类
  learning_categories JSONB DEFAULT '[]', -- 学习过的分类统计
  
  -- 学习效率
  completion_rate DECIMAL(5,2) DEFAULT 0, -- 完成率
  average_progress_speed DECIMAL(8,2) DEFAULT 0, -- 平均学习速度(章节/小时)
  retention_score DECIMAL(5,2) DEFAULT 0, -- 知识保持率评分
  
  -- 学习偏好
  preferred_learning_time VARCHAR(20), -- morning, afternoon, evening, night
  preferred_session_length INTEGER DEFAULT 30, -- 偏好学习时长(分钟)
  learning_style JSONB DEFAULT '{}', -- 学习风格分析
  
  -- 时间戳
  last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE(user_identifier)
);

-- 学习成就定义表
CREATE TABLE IF NOT EXISTS learning_achievements (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(50) NOT NULL, -- progress, time, quality, social, special
  
  -- 成就图标和样式
  icon_url TEXT,
  badge_color VARCHAR(7) DEFAULT '#3b82f6',
  rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  points INTEGER DEFAULT 10, -- 成就积分
  
  -- 解锁条件
  unlock_conditions JSONB NOT NULL, -- 解锁条件配置
  unlock_message TEXT, -- 解锁时显示的消息
  
  -- 状态
  is_active BOOLEAN DEFAULT true,
  is_hidden BOOLEAN DEFAULT false, -- 隐藏成就
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户成就记录表
CREATE TABLE IF NOT EXISTS user_achievements (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  achievement_id INTEGER NOT NULL REFERENCES learning_achievements(id) ON DELETE CASCADE,
  
  -- 解锁信息
  unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  progress_data JSONB, -- 解锁时的进度数据
  
  -- 社交功能
  is_public BOOLEAN DEFAULT true, -- 是否公开显示
  shared_at TIMESTAMP, -- 分享时间
  
  UNIQUE(user_identifier, achievement_id)
);

-- 学习路径模板表
CREATE TABLE IF NOT EXISTS learning_path_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  
  -- 路径配置
  tutorial_sequence JSONB NOT NULL, -- 教程序列配置
  estimated_duration INTEGER, -- 预估完成时间(小时)
  difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
  prerequisites TEXT[], -- 前置技能要求
  learning_objectives TEXT[], -- 学习目标
  
  -- 适用人群
  target_audience VARCHAR(100), -- 目标受众
  min_skill_level INTEGER DEFAULT 1,
  max_skill_level INTEGER DEFAULT 5,
  
  -- 状态
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  created_by VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户个性化学习路径表
CREATE TABLE IF NOT EXISTS user_learning_paths (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  template_id INTEGER REFERENCES learning_path_templates(id) ON DELETE SET NULL,
  
  -- 路径信息
  name VARCHAR(100) NOT NULL,
  description TEXT,
  custom_tutorial_sequence JSONB, -- 个性化教程序列
  
  -- 进度追踪
  current_position INTEGER DEFAULT 0, -- 当前位置
  total_tutorials INTEGER NOT NULL,
  completed_tutorials INTEGER DEFAULT 0,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  
  -- 时间管理
  target_completion_date DATE,
  estimated_time_remaining INTEGER, -- 预估剩余时间(小时)
  
  -- 状态
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'abandoned')),
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP,
  last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 学习会话表 (详细的学习行为记录)
CREATE TABLE IF NOT EXISTS learning_sessions (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(50) NOT NULL UNIQUE,
  user_identifier VARCHAR(100) NOT NULL,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  
  -- 会话信息
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ended_at TIMESTAMP,
  duration INTEGER, -- 会话时长(秒)
  is_active BOOLEAN DEFAULT true,
  
  -- 设备和环境
  device_type VARCHAR(20),
  screen_resolution VARCHAR(20),
  user_agent TEXT,
  ip_address INET,
  
  -- 学习行为数据
  interaction_events JSONB DEFAULT '[]', -- 交互事件序列
  scroll_events JSONB DEFAULT '[]', -- 滚动事件
  focus_events JSONB DEFAULT '[]', -- 焦点事件
  
  -- 性能指标
  page_load_time INTEGER, -- 页面加载时间(毫秒)
  total_scroll_distance INTEGER, -- 总滚动距离
  mouse_movement_distance INTEGER, -- 鼠标移动距离
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 学习数据分析模型

### 学习效率指标
```typescript
interface LearningEfficiencyMetrics {
  completionRate: number        // 完成率 (0-100)
  averageTimePerSection: number // 平均每章节学习时间
  retentionRate: number         // 知识保持率
  engagementScore: number       // 参与度评分
  difficultyAdaptationRate: number // 难度适应率
}

interface LearningBehaviorPattern {
  preferredLearningTime: string    // 偏好学习时间段
  averageSessionLength: number     // 平均学习时长
  interactionFrequency: number     // 交互频率
  pausePatterns: number[]          // 暂停模式分析
  devicePreference: string         // 设备偏好
}
```

### 个性化推荐算法
```typescript
interface RecommendationEngine {
  // 基于学习历史的推荐
  contentBasedRecommendation(userId: string): TutorialRecommendation[]
  
  // 基于相似用户的推荐
  collaborativeFiltering(userId: string): TutorialRecommendation[]
  
  // 基于学习路径的推荐
  pathBasedRecommendation(userId: string): LearningPathRecommendation[]
  
  // 难度自适应推荐
  adaptiveDifficultyRecommendation(userId: string): TutorialRecommendation[]
}
```

## 🏆 成就系统设计

### 成就类别和示例
```json
{
  "progress_achievements": [
    {
      "name": "first_tutorial",
      "display_name": "初学者",
      "description": "完成第一个教程",
      "unlock_conditions": {"tutorials_completed": 1},
      "points": 10
    },
    {
      "name": "tutorial_master",
      "display_name": "教程大师",
      "description": "完成100个教程",
      "unlock_conditions": {"tutorials_completed": 100},
      "points": 500
    }
  ],
  "time_achievements": [
    {
      "name": "night_owl",
      "display_name": "夜猫子",
      "description": "在晚上11点后学习超过10小时",
      "unlock_conditions": {
        "night_learning_hours": 10,
        "time_range": "23:00-06:00"
      },
      "points": 50
    }
  ],
  "streak_achievements": [
    {
      "name": "week_warrior",
      "display_name": "一周勇士",
      "description": "连续学习7天",
      "unlock_conditions": {"learning_streak_days": 7},
      "points": 100
    }
  ]
}
```

## 🔧 API接口设计

### 学习进度API
```typescript
// 记录学习进度
POST /api/learning/progress
{
  tutorialId: number,
  sectionId?: number,
  progressPercentage: number,
  timeSpent: number,
  interactionData: object
}

// 获取学习统计
GET /api/learning/stats/:userId
Response: UserLearningStats

// 获取学习路径推荐
GET /api/learning/recommendations/:userId
Response: TutorialRecommendation[]

// 获取用户成就
GET /api/learning/achievements/:userId
Response: UserAchievement[]
```

## 📊 数据仓库设计

### 学习分析数据立方体
```sql
-- 学习行为分析视图
CREATE VIEW learning_analytics_summary AS
SELECT 
  DATE(created_at) as learning_date,
  user_identifier,
  tutorial_id,
  COUNT(DISTINCT section_id) as sections_accessed,
  SUM(total_time_spent) as total_time,
  AVG(progress_percentage) as avg_progress,
  COUNT(DISTINCT learning_session_id) as session_count
FROM user_learning_records
GROUP BY DATE(created_at), user_identifier, tutorial_id;

-- 用户学习漏斗分析
CREATE VIEW learning_funnel_analysis AS
SELECT 
  tutorial_id,
  COUNT(DISTINCT CASE WHEN status = 'not_started' THEN user_identifier END) as not_started,
  COUNT(DISTINCT CASE WHEN status = 'in_progress' THEN user_identifier END) as in_progress,
  COUNT(DISTINCT CASE WHEN status = 'completed' THEN user_identifier END) as completed,
  ROUND(100.0 * COUNT(DISTINCT CASE WHEN status = 'completed' THEN user_identifier END) / 
        NULLIF(COUNT(DISTINCT user_identifier), 0), 2) as completion_rate
FROM user_learning_records
GROUP BY tutorial_id;
```

## 🚀 实施优先级

### Phase 1: 基础进度跟踪 (2周)
- [ ] 创建核心数据表
- [ ] 实现基础进度记录API
- [ ] 开发进度显示组件

### Phase 2: 学习分析 (2周) 
- [ ] 实现学习统计计算
- [ ] 开发学习仪表板
- [ ] 添加学习行为追踪

### Phase 3: 成就系统 (1周)
- [ ] 创建成就定义和规则
- [ ] 实现成就解锁逻辑
- [ ] 开发成就展示界面

### Phase 4: 智能推荐 (2周)
- [ ] 实现推荐算法
- [ ] 个性化学习路径
- [ ] A/B测试优化

---

*此数据模型将为知识商城提供完整的学习进度跟踪和个性化学习体验，支持数据驱动的教育优化。*