// 临时测试文件，用于验证 Supabase 连接
// 使用完毕后可以删除

import { supabaseAdmin } from './supabase'

export async function testSupabaseConnection() {
  try {
    console.log('🔄 正在测试 Supabase 连接...')
    
    // 测试读取分类数据
    const { data: categories, error } = await supabaseAdmin
      .from('categories')
      .select('*')
      .limit(5)
    
    if (error) {
      console.error('❌ 数据库连接失败:', error)
      return { success: false, error }
    }
    
    console.log('✅ 数据库连接成功!')
    console.log('📊 分类数据:', categories)
    
    return { success: true, data: categories }
    
  } catch (error) {
    console.error('❌ 连接测试异常:', error)
    return { success: false, error }
  }
}