{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(mkdir -p \"F:\\编程项目\\知识商城\\docs\")", "Bash(cd \"F:\\编程项目\\知识商城\")", "Bash(pnpm add @supabase/supabase-js)", "Bash(npm install bcryptjs)", "Bash(npm install @types/bcryptjs --save-dev)", "Bash(node scripts/generate-admin-hash.js admin123)", "Bash(npm install dotenv)", "Bash(curl -X POST http://localhost:3000/api/admin/auth -H \"Content-Type: application/json\" -d \"{\"\"password\"\":\"\"admin123\"\"}\")", "Bash(curl -X POST http://localhost:3000/api/admin/auth -H \"Content-Type: application/json\" -d \"{\"\"password\"\":\"\"wrongpassword\"\"}\")", "Bash(curl -X GET http://localhost:3000/api/test-db)", "Bash(node scripts/check-keys.js)", "Bash(node scripts/add-test-keys.js)", "Bash(curl -X POST http://localhost:3000/api/verify-key -H \"Content-Type: application/json\" -d \"{\"\"key\"\":\"\"NEXTJS-2024-ABC123\"\"}\")", "Bash(curl -X POST http://localhost:3000/api/verify-key -H \"Content-Type: application/json\" -d \"{\"\"key\"\":\"\"invalid_key\"\"}\")", "Bash(curl -X POST http://localhost:3000/api/verify-key -H \"Content-Type: application/json\" -d \"{\"\"key\"\":\"\"DESIGN-2024-JKL012\"\"}\")", "Bash(curl -X GET http://localhost:3000/api/public/categories)", "Bash(echo)", "Bash(curl -X GET http://localhost:3000/api/public/tutorials)", "Bash(curl -X GET \"http://localhost:3000/api/user-unlocks?user_identifier=test_user\")", "Bash(rm -f app/api/test-db/route.ts)", "<PERSON>sh(curl -X GET \"http://localhost:3000/api/public/tutorials\")", "Bash(curl -X GET \"http://localhost:3000/api/public/tutorials?page=1&limit=1\")", "Bash(curl -X GET \"http://localhost:3000/api/public/tutorials?search=Next.js\")", "Bash(curl -X GET \"http://localhost:3000/api/public/categories\")", "Bash(curl -X GET \"http://localhost:3000/api/user-unlocks?include_stats=true&limit=5\")", "Bash(npm install @tiptap/react @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-link @tiptap/extension-code-block-lowlight)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "Bash(npm run dev:*)", "Bash(curl -s -w \"%{http_code}\" http://localhost:3001/)", "Bash(pnpm install)", "Bash(npm install)", "<PERSON><PERSON>(curl -s http://localhost:3001)", "Bash(pkill -f \"next dev\")", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(rm -rf .next)", "<PERSON>sh(cp \"components/editor/TutorialEditor.tsx\" \"components/editor/TutorialEditor.tsx.backup\")", "<PERSON><PERSON>(curl -s http://localhost:3002)", "Bash(curl -s http://localhost:3002/admin/create-tutorial)", "mcp__firecrawl-mcp-server__firecrawl_search", "mcp__context7__resolve-library-id", "WebFetch(domain:developers.weixin.qq.com)", "Bash(pnpm dev)", "Bash(npm run build)", "Bash(node -e \"\nconst crypto = require(''crypto'');\n\nfunction generateUniqueKey() {\n  const timestamp = Date.now().toString(36);\n  const randomBytes = crypto.randomBytes(16).toString(''hex'');\n  const combined = timestamp + randomBytes;\n  const hash = crypto.createHash(''sha256'').update(combined).digest(''hex'');\n  return hash.substring(0, 24).toUpperCase();\n}\n\nfor(let i = 0; i < 5; i++) {\n  const key = generateUniqueKey();\n  console.log(''Key: '' + key + '' | Length: '' + key.length);\n}\n\")", "Bash(rm -rf \"F:\\编程项目\\知识商城\\app\\api\\debug\")", "Bash(pnpm build)", "Bash(node -e \"\nconst { supabaseAdmin } = require(''./lib/supabase.js'');\n(async () => {\n  try {\n    console.log(''🔍 检查announcements表结构...'');\n    const { data, error } = await supabaseAdmin\n      .from(''announcements'')\n      .select(''*'')\n      .limit(1);\n    \n    if (error) {\n      console.error(''❌ 表查询错误:'', error.message);\n      console.log(''可能原因: 表不存在或权限问题'');\n    } else {\n      console.log(''✅ announcements表存在，结构正常'');\n      console.log(''表中记录数:'', data.length);\n    }\n  } catch (err) {\n    console.error(''❌ 连接错误:'', err.message);\n  }\n})();\n\")", "Bash(curl -X POST http://localhost:3003/api/admin/announcements )", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"title\"\": \"\"测试公告\"\",\n    \"\"content\"\": \"\"这是一个测试公告内容\"\",\n    \"\"type\"\": \"\"info\"\",\n    \"\"priority\"\": 50,\n    \"\"is_active\"\": true,\n    \"\"target_audience\"\": \"\"all\"\"\n  }')", "Bash(npm install @tailwindcss/line-clamp)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click"], "deny": []}}