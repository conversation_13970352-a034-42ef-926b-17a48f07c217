# Supabase 迁移实施指南

## 概述

本文档详细说明如何将当前的 PostgreSQL 数据库迁移到 Supabase，并完成相关配置。

---

## 步骤 1: Supabase 项目创建

### 1.1 账户注册和项目创建
- [ ] 访问 [Supabase.com](https://supabase.com)
- [ ] 使用 GitHub 账户注册
- [ ] 点击 "New Project" 创建项目
- [ ] 选择组织 (如果是首次使用会自动创建)

### 1.2 项目配置
```
项目名称: knowledge-store
数据库密码: [生成强密码并保存]
区域: Southeast Asia (Singapore)
定价计划: Free Plan
```

### 1.3 获取连接信息
项目创建完成后，在设置页面获取以下信息：
- [ ] **Project URL**: `https://xxxxx.supabase.co`
- [ ] **API Keys**:
  - `anon` key (公开密钥)
  - `service_role` key (服务端密钥)
- [ ] **数据库连接字符串**

---

## 步骤 2: 数据库结构迁移

### 2.1 在 Supabase 中执行 SQL
1. [ ] 登录 Supabase Dashboard
2. [ ] 进入 SQL Editor
3. [ ] 执行 `scripts/01-create-tables.sql` 内容

```sql
-- 在 Supabase SQL Editor 中执行
-- 复制 scripts/01-create-tables.sql 的完整内容
```

### 2.2 验证表结构
- [ ] 在 Table Editor 中检查所有表是否创建成功
- [ ] 验证表关系和约束
- [ ] 检查索引是否正确创建

### 2.3 执行种子数据
```sql
-- 执行 scripts/02-seed-data.sql 内容
-- 或创建初始管理员数据
INSERT INTO system_config (config_key, config_value, description) 
VALUES ('admin_password', '$2b$10$hashed_password', '管理员密码哈希');
```

---

## 步骤 3: Row Level Security (RLS) 配置

### 3.1 启用 RLS
```sql
-- 为所有表启用 RLS
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutorials ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutorial_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_unlocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;
```

### 3.2 创建安全策略

#### 公开读取策略 (分类和已发布教程)
```sql
-- 分类表：所有用户可读
CREATE POLICY "categories_public_read" ON categories
    FOR SELECT USING (true);

-- 教程表：仅已发布教程可公开读取
CREATE POLICY "tutorials_public_read" ON tutorials
    FOR SELECT USING (status = 'published');
```

#### 管理员策略
```sql
-- 管理员可以操作所有数据
CREATE POLICY "admin_all_access" ON tutorials
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin'
    );

-- 类似地为其他表创建管理员策略
```

#### 用户策略
```sql
-- 用户只能查看自己的解锁记录
CREATE POLICY "user_unlocks_owner" ON user_unlocks
    FOR SELECT USING (
        user_identifier = auth.jwt() ->> 'user_id'
    );
```

### 3.3 服务角色策略
```sql
-- 为服务端操作创建绕过策略
CREATE POLICY "service_role_bypass" ON tutorial_keys
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'service_role'
    );
```

---

## 步骤 4: 项目代码更新

### 4.1 安装 Supabase 依赖
```bash
pnpm add @supabase/supabase-js
```

### 4.2 创建 Supabase 客户端配置

#### 创建 `lib/supabase.ts`
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// 前端客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 服务端客户端 (用于 API 路由)
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)
```

### 4.3 更新环境变量

#### `.env.local` 文件配置
```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 保留备用 (逐步迁移期间)
DATABASE_URL=postgresql://...
```

### 4.4 更新数据库连接

#### 修改 `lib/database.ts`
```typescript
import { supabaseAdmin } from './supabase'

// 新的查询函数
export async function query(text: string, params?: any[]) {
  try {
    // 使用 Supabase 客户端执行原生 SQL
    const { data, error } = await supabaseAdmin.rpc('exec_sql', {
      query: text,
      params: params || []
    })
    
    if (error) throw error
    return { rows: data }
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  }
}

// 或者逐步迁移到 Supabase 的查询方式
export async function getTutorials() {
  const { data, error } = await supabaseAdmin
    .from('tutorials')
    .select('*')
    .eq('status', 'published')
  
  if (error) throw error
  return data
}
```

---

## 步骤 5: API 路由迁移

### 5.1 认证中间件更新

#### 创建 `lib/auth-middleware.ts`
```typescript
import { supabaseAdmin } from './supabase'
import { NextRequest } from 'next/server'

export async function verifyAuth(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')
  
  if (!token) {
    throw new Error('No authorization token')
  }
  
  const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)
  
  if (error || !user) {
    throw new Error('Invalid token')
  }
  
  return user
}
```

### 5.2 API 路由逐步迁移

#### 示例：更新 `app/api/public/tutorials/route.ts`
```typescript
import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET() {
  try {
    const { data: tutorials, error } = await supabaseAdmin
      .from('tutorials')
      .select(`
        *,
        categories!inner(name)
      `)
      .eq('status', 'published')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    
    return NextResponse.json(tutorials)
  } catch (error) {
    console.error('Error fetching tutorials:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tutorials' },
      { status: 500 }
    )
  }
}
```

---

## 步骤 6: 测试和验证

### 6.1 功能测试清单
- [ ] **数据库连接测试**
  - [ ] 启动开发服务器
  - [ ] 检查控制台是否有数据库连接错误
  - [ ] 测试基本的 CRUD 操作

- [ ] **API 接口测试**
  - [ ] 测试 `/api/public/tutorials` 接口
  - [ ] 测试 `/api/public/categories` 接口
  - [ ] 测试密钥验证接口

- [ ] **前端功能测试**
  - [ ] 首页教程列表加载
  - [ ] 分类筛选功能
  - [ ] 密钥验证功能
  - [ ] 管理后台访问

### 6.2 性能测试
- [ ] 检查页面加载时间
- [ ] 测试数据库查询响应时间
- [ ] 验证 RLS 策略性能影响

### 6.3 安全测试
- [ ] 测试 RLS 策略是否生效
- [ ] 验证未授权访问被正确拒绝
- [ ] 检查敏感数据不会泄露

---

## 步骤 7: 生产环境配置

### 7.1 Supabase 项目设置优化
- [ ] 设置自定义域名 (如需要)
- [ ] 配置邮件模板
- [ ] 设置 CORS 允许的域名
- [ ] 配置 Rate Limiting

### 7.2 监控和告警
- [ ] 启用 Supabase 的监控功能
- [ ] 设置数据库使用量告警
- [ ] 配置错误日志监控

### 7.3 备份策略
- [ ] 确认 Supabase 自动备份设置
- [ ] 制定数据导出计划
- [ ] 测试数据恢复流程

---

## 迁移时间表

| 阶段 | 预计时间 | 任务 |
|------|----------|------|
| 准备阶段 | 1天 | 创建 Supabase 项目，获取配置信息 |
| 数据迁移 | 2天 | 执行 SQL 脚本，配置 RLS 策略 |
| 代码更新 | 3天 | 更新项目代码，迁移 API 路由 |
| 测试验证 | 2天 | 功能测试，性能测试，安全测试 |
| 生产配置 | 1天 | 优化设置，配置监控 |

**总计**: 9天

---

## 回滚计划

如果迁移过程中出现问题，可以快速回滚：

1. [ ] 保留原有的 `DATABASE_URL` 配置
2. [ ] 临时注释 Supabase 相关代码
3. [ ] 恢复原有的数据库连接逻辑
4. [ ] 验证系统功能正常

---

## 注意事项

### ⚠️ 重要提醒
1. **数据备份**: 迁移前务必备份现有数据
2. **环境变量**: 确保生产环境的环境变量正确配置
3. **RLS 策略**: 仔细测试安全策略，避免数据泄露
4. **免费额度**: 监控 Supabase 使用量，避免超出免费额度

### 🔧 故障排除
- **连接失败**: 检查网络和防火墙设置
- **权限错误**: 验证 RLS 策略和 API Keys
- **性能问题**: 检查查询索引和数据量

---

*本文档将根据实际迁移经验持续更新。*