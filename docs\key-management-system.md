# 密钥管理系统功能说明

## 功能概述

全新的密钥管理系统已实现，具备以下核心特性：

### ✅ 已实现功能

1. **以教程为单位的密钥管理**
   - 每个教程独立管理密钥
   - 展开/折叠式界面设计
   - 实时统计信息显示

2. **密钥唯一性保证**
   - 数据库层面的唯一性验证
   - 重复密钥自动重新生成
   - 并发安全的密钥生成

3. **批量导出功能**
   - 支持批量导出未使用密钥
   - 导出格式：每行一个密钥
   - 导出后密钥自动标记为"已导出"状态
   - 已导出密钥不再显示在管理界面

4. **完整的密钥状态管理**
   - **未使用(unused)**: 新生成的密钥
   - **已使用(used)**: 已被用户兑换的密钥
   - **已导出(exported)**: 已批量导出的密钥
   - **已过期(expired)**: 超时失效的密钥

5. **统计信息展示**
   - 总密钥数量
   - 可用密钥数量
   - 已兑换密钥数量
   - 已导出密钥数量

## 界面功能说明

### 1. 主界面
- 以教程为单位显示密钥信息
- 点击教程行可展开查看详细密钥列表
- 统计信息一目了然

### 2. 密钥生成
- 点击"生成密钥"按钮
- 选择目标教程（仅显示已发布的教程）
- 输入生成数量（1-100个）
- 自动确保密钥唯一性

### 3. 批量导出
- 点击"批量导出"按钮
- 选择有可用密钥的教程
- 输入导出数量（1-1000个）
- 自动下载TXT文件
- 导出的密钥自动标记为"已导出"

### 4. 密钥详情
- 展开教程后可查看未导出的密钥列表
- 显示密钥状态、创建时间、使用时间
- 支持单个密钥复制到剪贴板

## 数据库更新

需要运行数据库更新脚本以支持新功能：

```sql
-- 文件: scripts/07-update-keys-table.sql
-- 添加exported状态支持
-- 添加exported_at字段
-- 创建相关索引和视图
```

## 安全特性

### 1. 密钥唯一性
- 使用SHA-256哈希算法生成
- 时间戳+随机数确保唯一性
- 数据库约束防止重复

### 2. 状态管理
- 严格的状态转换控制
- 已导出密钥不可撤销
- 完整的操作日志记录

### 3. 权限控制
- 仅管理员可操作
- API层面的参数验证
- 防止恶意批量操作

## 技术实现

### 1. 后端API
- `/api/admin/keys` - 获取密钥统计信息
- `/api/admin/keys/[tutorialId]` - 获取特定教程密钥
- `/api/admin/keys/generate` - 生成新密钥
- `/api/admin/keys/[tutorialId]/export` - 批量导出密钥

### 2. 前端组件
- `TutorialKeysManager` - 主管理组件
- 响应式设计，支持PC和移动端
- 实时状态更新和错误处理

### 3. 数据库结构
- 扩展`tutorial_keys`表支持导出状态
- 添加导出时间字段
- 创建统计视图便于查询

## 使用流程

### 管理员操作流程
1. **生成密钥**：选择教程 → 输入数量 → 确认生成
2. **批量导出**：选择教程 → 输入数量 → 下载文件
3. **查看统计**：查看各教程的密钥使用情况
4. **管理密钥**：展开教程查看详细密钥列表

### 用户使用流程
1. 用户从管理员处获得密钥文件
2. 在网站首页输入密钥进行兑换
3. 成功兑换后可查看对应教程内容

## 注意事项

### 1. 导出限制
- 已导出的密钥不会重复显示
- 导出数量不能超过可用密钥数量
- 导出操作不可撤销

### 2. 密钥安全
- 生成的密钥立即可用
- 定期检查密钥使用情况
- 避免批量导出过多密钥

### 3. 性能考虑
- 大量密钥生成时可能需要时间
- 建议分批次生成和导出
- 定期清理过期密钥

---

**系统版本**: v2.0  
**更新时间**: 2025年1月  
**开发者**: Backend Persona