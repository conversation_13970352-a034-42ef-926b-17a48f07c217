import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: { tutorialId: string } }
) {
  try {
    const tutorialId = parseInt(params.tutorialId)
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '5')
    const offset = (page - 1) * pageSize

    if (!tutorialId) {
      return NextResponse.json({ error: "Invalid tutorial ID" }, { status: 400 })
    }

    // 验证教程是否存在
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 })
    }

    // 获取该教程的所有未导出密钥（分页）
    // 排序优先级：未使用密钥优先，然后按创建时间降序（最新生成的密钥在前）
    const { data: keys, error: keysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select(`
        id,
        key_code,
        status,
        created_at,
        used_at
      `)
      .eq('tutorial_id', tutorialId)
      .neq('status', 'exported')
      .order('status', { ascending: true })  // unused < used（字母序）
      .order('created_at', { ascending: false })  // 最新的在前
      .range(offset, offset + pageSize - 1)

    if (keysError) {
      console.error("Get tutorial keys error:", keysError)
      return NextResponse.json({ error: "Failed to fetch keys" }, { status: 500 })
    }

    // 获取总数用于分页计算
    const { count: totalCount, error: countError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .neq('status', 'exported')

    if (countError) {
      console.error("Get keys count error:", countError)
      return NextResponse.json({ error: "Failed to fetch keys count" }, { status: 500 })
    }

    // 获取各状态密钥统计信息
    const { count: totalKeys } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)

    const { count: usedKeys } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .eq('status', 'used')

    const { count: exportedKeys } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .eq('status', 'exported')

    const { count: unusedKeys } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .eq('status', 'unused')

    const totalPages = Math.ceil((totalCount || 0) / pageSize)

    return NextResponse.json({
      success: true,
      data: {
        tutorial,
        keys,
        pagination: {
          current_page: page,
          page_size: pageSize,
          total_count: totalCount || 0,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1
        },
        stats: {
          total_keys: totalKeys || 0,
          used_keys: usedKeys || 0,
          exported_keys: exportedKeys || 0,
          unused_keys: unusedKeys || 0,
          available_keys: (totalCount || 0) // 可显示的密钥数量（排除已导出）
        }
      }
    })
  } catch (error) {
    console.error("Get tutorial keys error:", error)
    return NextResponse.json({ error: "Failed to fetch tutorial keys" }, { status: 500 })
  }
}