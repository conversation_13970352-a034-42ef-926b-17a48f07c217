#!/usr/bin/env node

/**
 * 管理员密码哈希生成工具
 * 用于生成bcrypt密码哈希并更新到数据库
 * 
 * 使用方法：
 * node scripts/generate-admin-hash.js [新密码]
 * 
 * 如果不提供密码参数，会提示输入
 */

const bcrypt = require('bcryptjs');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// 创建Supabase客户端
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function generateAdminPasswordHash(password) {
  try {
    console.log('🔐 正在生成管理员密码哈希...');
    
    // 生成bcrypt哈希 (12 rounds for high security)
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    console.log('✅ 密码哈希生成成功:');
    console.log(`   哈希值: ${passwordHash}`);
    console.log(`   长度: ${passwordHash.length} 字符`);
    
    return passwordHash;
  } catch (error) {
    console.error('❌ 生成密码哈希失败:', error);
    process.exit(1);
  }
}

async function updateAdminPasswordInDB(passwordHash) {
  try {
    console.log('📝 正在更新数据库中的管理员密码...');
    
    // 删除现有配置
    await supabaseAdmin
      .from('system_config')
      .delete()
      .eq('config_key', 'admin_password');
    
    // 插入新的密码哈希
    const { error } = await supabaseAdmin
      .from('system_config')
      .insert({
        config_key: 'admin_password',
        config_value: passwordHash,
        description: '管理员登录密码哈希值 - bcrypt加密',
        created_at: new Date().toISOString()
      });
    
    if (error) {
      throw error;
    }
    
    console.log('✅ 数据库更新成功');
    
    // 验证更新
    const { data: verification } = await supabaseAdmin
      .from('system_config')
      .select('config_key, description, created_at')
      .eq('config_key', 'admin_password')
      .single();
    
    if (verification) {
      console.log('🔍 验证结果:');
      console.log(`   配置键: ${verification.config_key}`);
      console.log(`   描述: ${verification.description}`);
      console.log(`   创建时间: ${verification.created_at}`);
    }
    
  } catch (error) {
    console.error('❌ 更新数据库失败:', error);
    process.exit(1);
  }
}

async function testPasswordVerification(password, hash) {
  try {
    console.log('🧪 测试密码验证...');
    
    const isValid = await bcrypt.compare(password, hash);
    
    if (isValid) {
      console.log('✅ 密码验证测试通过');
    } else {
      console.log('❌ 密码验证测试失败');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 密码验证测试异常:', error);
    process.exit(1);
  }
}

async function main() {
  try {
    // 获取密码参数
    let password = process.argv[2];
    
    if (!password) {
      console.log('请输入新的管理员密码:');
      // 简化版本，生产环境应使用更安全的输入方法
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      password = await new Promise((resolve) => {
        rl.question('新密码: ', (answer) => {
          rl.close();
          resolve(answer);
        });
      });
    }
    
    // 验证密码强度
    if (password.length < 8) {
      console.error('❌ 密码长度至少需要8位字符');
      process.exit(1);
    }
    
    console.log(`📋 操作摘要:`);
    console.log(`   密码长度: ${password.length} 字符`);
    console.log(`   哈希轮数: 12 rounds (高安全性)`);
    console.log(`   目标环境: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Supabase' : '未配置'}`);
    console.log('');
    
    // 生成密码哈希
    const passwordHash = await generateAdminPasswordHash(password);
    
    // 测试密码验证
    await testPasswordVerification(password, passwordHash);
    
    // 更新数据库
    await updateAdminPasswordInDB(passwordHash);
    
    console.log('');
    console.log('🎉 管理员密码设置完成！');
    console.log('');
    console.log('⚠️ 安全提醒:');
    console.log('1. 请妥善保管新密码');
    console.log('2. 定期更换管理员密码');
    console.log('3. 避免使用弱密码');
    console.log('4. 生产环境中启用双因素认证');
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  generateAdminPasswordHash,
  updateAdminPasswordInDB,
  testPasswordVerification
};