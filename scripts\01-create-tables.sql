-- 创建数据库表结构

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 教程商品表
CREATE TABLE IF NOT EXISTS tutorials (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  category_id INTEGER REFERENCES categories(id),
  tags TEXT[], -- PostgreSQL数组类型存储标签
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  price DECIMAL(10,2) DEFAULT 0,
  thumbnail_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 密钥表
CREATE TABLE IF NOT EXISTS tutorial_keys (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  key_code VARCHAR(32) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'unused' CHECK (status IN ('unused', 'used', 'expired')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  used_at TIMESTAMP,
  expires_at TIMESTAMP,
  user_identifier VARCHAR(100) -- 用于记录使用密钥的用户标识
);

-- 用户解锁记录表
CREATE TABLE IF NOT EXISTS user_unlocks (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id),
  key_id INTEGER NOT NULL REFERENCES tutorial_keys(id),
  unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ip_address INET,
  user_agent TEXT,
  UNIQUE(user_identifier, tutorial_id) -- 防止同一用户重复解锁同一教程
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
  id SERIAL PRIMARY KEY,
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value TEXT,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tutorials_category ON tutorials(category_id);
CREATE INDEX IF NOT EXISTS idx_tutorials_status ON tutorials(status);
CREATE INDEX IF NOT EXISTS idx_keys_tutorial ON tutorial_keys(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_keys_status ON tutorial_keys(status);
CREATE INDEX IF NOT EXISTS idx_unlocks_user ON user_unlocks(user_identifier);
CREATE INDEX IF NOT EXISTS idx_unlocks_tutorial ON user_unlocks(tutorial_id);
