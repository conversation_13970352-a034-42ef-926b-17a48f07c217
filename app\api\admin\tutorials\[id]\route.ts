import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: tutorial, error } = await supabaseAdmin
      .from('tutorials')
      .select(`
        *,
        categories!category_id(name)
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      console.error("Supabase error:", error)
      return NextResponse.json({ error: "教程不存在" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...tutorial,
        category_name: tutorial.categories?.name || '未分类'
      }
    })
  } catch (error) {
    console.error("Get tutorial error:", error)
    return NextResponse.json({ error: "Failed to fetch tutorial" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { title, description, content, category_id, tags, price, status } = await request.json()

    // 验证必填字段
    if (!title || !description || !content || !category_id) {
      return NextResponse.json(
        { error: "缺少必填字段" }, 
        { status: 400 }
      )
    }

    // 默认发布状态为 published，除非明确设置为 draft
    const tutorialStatus = status === 'draft' ? 'draft' : 'published'

    const { data: tutorial, error } = await supabaseAdmin
      .from('tutorials')
      .update({
        title,
        description,
        content,
        category_id: parseInt(category_id),
        tags: tags || [],
        price: parseFloat(price) || 0,
        status: tutorialStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select(`
        *,
        categories!category_id(name)
      `)
      .single()

    if (error) {
      console.error("Supabase error:", error)
      return NextResponse.json({ error: "Failed to update tutorial" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...tutorial,
        category_name: tutorial.categories?.name || '未分类'
      }
    })
  } catch (error) {
    console.error("Update tutorial error:", error)
    return NextResponse.json({ error: "Failed to update tutorial" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const tutorialId = parseInt(params.id)
    const { searchParams } = new URL(request.url)
    const deleteMode = searchParams.get('mode') || 'archive' // 默认为下架模式
    
    if (!tutorialId) {
      return NextResponse.json({ error: "无效的教程ID" }, { status: 400 })
    }

    // 检查教程是否存在
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title, status')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "教程不存在" }, { status: 404 })
    }

    // 检查关联数据
    const [keysResult, unlocksResult] = await Promise.all([
      supabaseAdmin
        .from('tutorial_keys')
        .select('*', { count: 'exact', head: true })
        .eq('tutorial_id', tutorialId),
      supabaseAdmin
        .from('user_unlocks')
        .select('*', { count: 'exact', head: true })
        .eq('tutorial_id', tutorialId)
    ])

    const keyCount = keysResult.count || 0
    const unlockCount = unlocksResult.count || 0

    if (keysResult.error || unlocksResult.error) {
      console.error("检查关联数据错误:", { keysError: keysResult.error, unlocksError: unlocksResult.error })
      return NextResponse.json({ error: "检查关联数据时出错" }, { status: 500 })
    }

    let result
    let successMessage

    switch (deleteMode) {
      case 'archive':
        // 下架：标记为下架状态，保留所有数据
        result = await supabaseAdmin
          .from('tutorials')
          .update({ 
            status: 'archived',
            updated_at: new Date().toISOString()
          })
          .eq('id', tutorialId)
        
        successMessage = `教程"${tutorial.title}"已下架，用户解锁记录和密钥数据已保留`
        break

      case 'force':
        // 强制删除：级联删除所有相关数据
        try {
          // 开始事务性删除
          const deletePromises = []

          // 删除学习记录
          if (unlockCount > 0) {
            deletePromises.push(
              supabaseAdmin
                .from('user_learning_records')
                .delete()
                .eq('tutorial_id', tutorialId)
            )
          }

          // 删除用户解锁记录
          if (unlockCount > 0) {
            deletePromises.push(
              supabaseAdmin
                .from('user_unlocks')
                .delete()
                .eq('tutorial_id', tutorialId)
            )
          }

          // 删除教程密钥
          if (keyCount > 0) {
            deletePromises.push(
              supabaseAdmin
                .from('tutorial_keys')
                .delete()
                .eq('tutorial_id', tutorialId)
            )
          }

          // 删除教程章节（如果有）
          deletePromises.push(
            supabaseAdmin
              .from('tutorial_sections')
              .delete()
              .eq('tutorial_id', tutorialId)
          )

          // 执行所有删除操作
          await Promise.all(deletePromises)

          // 最后删除教程本身
          result = await supabaseAdmin
            .from('tutorials')
            .delete()
            .eq('id', tutorialId)

          successMessage = `教程"${tutorial.title}"及所有相关数据已彻底删除`
        } catch (forceDeleteError) {
          console.error("强制删除失败:", forceDeleteError)
          return NextResponse.json({ 
            error: "强制删除失败，请联系技术支持" 
          }, { status: 500 })
        }
        break

      default:
        return NextResponse.json({ 
          error: `无效的删除模式: ${deleteMode}。仅支持 'archive' 或 'force'` 
        }, { status: 400 })
    }

    if (result.error) {
      console.error("删除操作失败:", result.error)
      return NextResponse.json({ 
        error: "删除操作失败，请稍后重试" 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: successMessage,
      metadata: {
        mode: deleteMode,
        tutorial_id: tutorialId,
        keys_affected: keyCount,
        unlocks_affected: unlockCount
      }
    })

  } catch (error) {
    console.error("删除教程异常:", error)
    return NextResponse.json({ 
      error: "服务器内部错误" 
    }, { status: 500 })
  }
}