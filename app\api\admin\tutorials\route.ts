import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import { CacheManager, CACHE_TAGS } from "@/lib/cache-manager"

export async function GET() {
  try {
    const { data: tutorials, error } = await supabaseAdmin
      .from('tutorials')
      .select(`
        *,
        categories!category_id(name)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error("Supabase error:", error)
      return NextResponse.json({ error: "Failed to fetch tutorials" }, { status: 500 })
    }

    // 格式化数据，添加category_name字段
    const formattedTutorials = tutorials.map(tutorial => ({
      ...tutorial,
      category_name: tutorial.categories?.name || '未分类'
    }))

    return NextResponse.json({
      success: true,
      data: formattedTutorials
    })
  } catch (error) {
    console.error("Get tutorials error:", error)
    return NextResponse.json({ error: "Failed to fetch tutorials" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { title, description, content, category_id, tags, price, status = 'published' } = await request.json()

    // 验证必填字段
    if (!title || !description || !content || !category_id) {
      return NextResponse.json(
        { error: "缺少必填字段" }, 
        { status: 400 }
      )
    }

    // 默认发布状态为 published，除非明确设置为 draft
    const tutorialStatus = status === 'draft' ? 'draft' : 'published'

    console.log('🔄 开始创建教程:', { title, status: tutorialStatus })

    const { data: tutorial, error } = await supabaseAdmin
      .from('tutorials')
      .insert({
        title,
        description,
        content,
        category_id: parseInt(category_id),
        tags: tags || [],
        price: parseFloat(price) || 0,
        status: tutorialStatus,
        created_at: new Date().toISOString()
      })
      .select(`
        *,
        categories!category_id(name)
      `)
      .single()

    if (error) {
      console.error("Supabase error:", error)
      return NextResponse.json({ error: "Failed to create tutorial" }, { status: 500 })
    }

    console.log('✅ 教程创建成功:', tutorial.id)

    // 🚀 立即失效相关缓存
    try {
      console.log('🔄 开始失效缓存...')
      
      // 失效教程相关缓存
      await CacheManager.invalidateTutorials({
        type: 'all',
        tutorialId: tutorial.id,
        categoryId: parseInt(category_id)
      })
      
      console.log('✅ 缓存失效完成 - 首页将立即显示新教程')
      
    } catch (cacheError) {
      console.error('⚠️ 缓存失效失败，但教程创建成功:', cacheError)
      // 不要因为缓存失效失败而让整个请求失败
    }

    // 返回成功响应
    const responseData = {
      success: true,
      data: {
        ...tutorial,
        category_name: tutorial.categories?.name || '未分类'
      },
      cache_invalidated: true // 标识缓存已失效
    }

    return NextResponse.json(responseData, {
      headers: {
        'Cache-Control': 'no-cache, must-revalidate',
        'X-Cache-Invalidated': 'true',
        'X-Tutorial-Created': tutorial.id.toString()
      }
    })

  } catch (error) {
    console.error("Create tutorial error:", error)
    return NextResponse.json({ 
      error: "Failed to create tutorial",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
