/**
 * 格式兼容性测试
 * 验证学习进度跟踪系统对不同内容格式的支持
 */

// 简化的HTML解析函数（不依赖JSDOM）
function parseContentSections(htmlContent) {
  const sections = [];
  
  // 使用正则表达式提取section信息
  const sectionRegex = /<(?:header|section)[^>]*data-section=["']([^"']*)["'][^>]*>/g;
  const titleRegex = /data-section-title=["']([^"']*)["']/;
  const timeRegex = /data-estimated-time=["']([^"']*)["']/;
  
  let match;
  let index = 0;
  
  while ((match = sectionRegex.exec(htmlContent)) !== null) {
    const fullMatch = match[0];
    const id = match[1] || `section-${index}`;
    
    // 提取标题
    const titleMatch = titleRegex.exec(fullMatch);
    const title = titleMatch ? titleMatch[1] : `章节 ${index + 1}`;
    
    // 提取预估时间
    const timeMatch = timeRegex.exec(fullMatch);
    const estimatedTime = timeMatch ? parseInt(timeMatch[1]) : 5;
    
    // 确定章节类型
    let type = 'chapter';
    if (id === 'intro') type = 'intro';
    else if (id === 'conclusion') type = 'conclusion';
    else if (fullMatch.includes('checkpoint')) type = 'checkpoint';
    else if (fullMatch.includes('interactive')) type = 'interactive';
    
    sections.push({
      id,
      title,
      estimatedTime,
      type,
      completed: false,
      currentlyViewing: false
    });
    
    index++;
  }
  
  return sections;
}

// 测试函数
function runCompatibilityTest() {
  console.log('🧪 开始格式兼容性测试...\n');
  
  // 测试1: HTML格式解析
  console.log('📄 测试1: HTML格式内容解析');
  const htmlContent = `
    <div>
      <header data-section="intro" data-section-title="课程介绍" data-estimated-time="5">
        <h1>课程介绍</h1>
      </header>
      <section data-section="1" data-section-title="基础知识" data-estimated-time="20">
        <h2>第一章</h2>
        <div data-checkpoint="1-1" data-checkpoint-type="knowledge">
          <h4>检查点</h4>
        </div>
      </section>
      <section data-section="conclusion" data-section-title="总结" data-estimated-time="10">
        <h2>课程总结</h2>
      </section>
    </div>
  `;
  
  const htmlSections = parseContentSections(htmlContent);
  console.log(`✅ 解析出 ${htmlSections.length} 个章节:`);
  htmlSections.forEach(section => {
    console.log(`   - ${section.id}: ${section.title} (${section.estimatedTime}分钟, 类型: ${section.type})`);
  });
  
  // 测试2: TipTap JSON格式模拟
  console.log('\n📝 测试2: TipTap JSON格式处理');
  const tiptapJSON = {
    "type": "doc",
    "content": [
      {
        "type": "heading",
        "attrs": { "level": 1 },
        "content": [{ "type": "text", "text": "课程介绍" }]
      },
      {
        "type": "paragraph",
        "content": [{ "type": "text", "text": "这是课程介绍内容..." }]
      },
      {
        "type": "heading", 
        "attrs": { "level": 2 },
        "content": [{ "type": "text", "text": "第一章：基础知识" }]
      }
    ]
  };
  
  // 将TipTap JSON转换为HTML的模拟函数
  function convertTipTapToHTML(json) {
    let html = '<div>';
    let sectionIndex = 0;
    
    json.content.forEach((node, index) => {
      if (node.type === 'heading' && node.attrs.level <= 2) {
        const isIntro = node.content[0].text.includes('介绍');
        const isConclusion = node.content[0].text.includes('总结');
        let sectionId = isIntro ? 'intro' : isConclusion ? 'conclusion' : `${++sectionIndex}`;
        
        html += `<section data-section="${sectionId}" data-section-title="${node.content[0].text}" data-estimated-time="15">`;
        html += `<h${node.attrs.level}>${node.content[0].text}</h${node.attrs.level}>`;
      } else if (node.type === 'paragraph') {
        html += `<p>${node.content[0].text}</p>`;
      }
    });
    
    html += '</section></div>';
    return html;
  }
  
  const convertedHTML = convertTipTapToHTML(tiptapJSON);
  const jsonSections = parseContentSections(convertedHTML);
  console.log(`✅ JSON转HTML后解析出 ${jsonSections.length} 个章节:`);
  jsonSections.forEach(section => {
    console.log(`   - ${section.id}: ${section.title} (${section.estimatedTime}分钟, 类型: ${section.type})`);
  });
  
  // 测试3: 进度计算
  console.log('\n📊 测试3: 进度计算功能');
  
  function calculateProgressPercentage(sections, completedSections) {
    if (!sections.length) return 0;
    
    const totalWeight = sections.reduce((sum, section) => sum + section.estimatedTime, 0);
    const completedWeight = sections
      .filter(section => completedSections.includes(section.id))
      .reduce((sum, section) => sum + section.estimatedTime, 0);
    
    return Math.round((completedWeight / totalWeight) * 100);
  }
  
  const testSections = htmlSections;
  const completedIds = ['intro', '1'];
  const progress = calculateProgressPercentage(testSections, completedIds);
  
  console.log(`✅ 完成章节: ${completedIds.join(', ')}`);
  console.log(`✅ 总进度: ${progress}%`);
  
  // 测试4: 兼容性检查
  console.log('\n🔍 测试4: 兼容性验证');
  
  const issues = [];
  
  // 检查章节ID唯一性
  const sectionIds = htmlSections.map(s => s.id);
  const duplicateIds = sectionIds.filter((id, index) => sectionIds.indexOf(id) !== index);
  if (duplicateIds.length > 0) {
    issues.push(`重复的章节ID: ${duplicateIds.join(', ')}`);
  }
  
  // 检查必需属性
  htmlSections.forEach(section => {
    if (!section.title) {
      issues.push(`章节 ${section.id} 缺少标题`);
    }
    if (section.estimatedTime <= 0) {
      issues.push(`章节 ${section.id} 学习时间无效`);
    }
  });
  
  if (issues.length === 0) {
    console.log('✅ 所有兼容性检查通过');
  } else {
    console.log('⚠️ 发现兼容性问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log('\n🎉 格式兼容性测试完成！');
  
  return {
    htmlParsing: htmlSections.length > 0,
    jsonConversion: jsonSections.length > 0,
    progressCalculation: progress >= 0,
    compatibilityIssues: issues.length,
    totalTests: 4,
    passedTests: (htmlSections.length > 0 ? 1 : 0) + 
                 (jsonSections.length > 0 ? 1 : 0) + 
                 (progress >= 0 ? 1 : 0) + 
                 (issues.length === 0 ? 1 : 0)
  };
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runCompatibilityTest, parseContentSections };
}

// 如果直接运行
if (require.main === module) {
  try {
    const results = runCompatibilityTest();
    console.log(`\n📈 测试总结: ${results.passedTests}/${results.totalTests} 项测试通过`);
    process.exit(results.passedTests === results.totalTests ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    process.exit(1);
  }
}