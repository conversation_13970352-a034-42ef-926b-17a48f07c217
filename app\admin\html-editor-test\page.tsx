"use client"

import { useState, useEffect } from 'react'
import { HTMLTutorialEditor } from '@/components/editor/HTMLTutorialEditor'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { ErrorBoundary } from '@/components/ui/error-boundary'

export default function HTMLEditorTestPage() {
  const [content, setContent] = useState('')
  const [savedContent, setSavedContent] = useState('')
  const { toast } = useToast()

  // 加载测试内容
  useEffect(() => {
    const loadTestContent = async () => {
      try {
        const response = await fetch('/test-html-content.html')
        if (response.ok) {
          const htmlContent = await response.text()
          // 提取body内容
          const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i)
          if (bodyMatch) {
            const bodyContent = bodyMatch[1]
            // 移除script标签，只保留HTML结构
            const cleanContent = bodyContent.replace(/<script[\s\S]*?<\/script>/gi, '')
            setContent(cleanContent.trim())
          }
        }
      } catch (error) {
        console.error('Failed to load test content:', error)
        // 使用简单的测试内容
        setContent(`
<section data-section="intro" data-section-title="HTML编辑器测试" data-estimated-time="5">
  <h2>HTML编辑器功能测试</h2>
  <p>这是一个测试HTML渲染功能的示例内容。</p>
  
  <div class="tip" style="background: #e7f3ff; border-left: 4px solid #0066cc; padding: 15px; margin: 20px 0; border-radius: 4px;">
    <strong>💡 提示：</strong>
    <p>你可以直接编辑HTML代码，并实时预览渲染效果。</p>
  </div>
  
  <h3>支持的功能</h3>
  <ul>
    <li><strong>富文本格式</strong>：标题、段落、列表等</li>
    <li><strong>自定义样式</strong>：颜色、背景、边框等</li>
    <li><strong>交互元素</strong>：按钮、输入框、链接等</li>
    <li><strong>数据属性</strong>：章节标记、时间估算等</li>
  </ul>
  
  <div class="warning" style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 4px;">
    <strong>⚠️ 注意：</strong>
    <p>HTML内容会直接渲染，请确保代码安全。</p>
  </div>
  
  <div data-checkpoint="test-1" data-checkpoint-type="knowledge" data-points="10" style="background: #f0f9ff; border: 2px solid #0ea5e9; padding: 20px; margin: 20px 0; border-radius: 8px;">
    <h4>🎯 测试检查点</h4>
    <p>尝试修改这段HTML代码，看看预览效果如何变化。</p>
  </div>
</section>

<section data-section="features" data-section-title="功能特性" data-estimated-time="10">
  <h2>编辑器功能特性</h2>
  
  <h3>三种编辑模式</h3>
  <ol>
    <li><strong>可视化编辑</strong>：使用TipTap富文本编辑器</li>
    <li><strong>HTML源码</strong>：直接编辑HTML代码</li>
    <li><strong>实时预览</strong>：查看渲染后的效果</li>
  </ol>
  
  <pre style="background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; overflow-x: auto;"><code>&lt;div class="interactive"&gt;
  &lt;h4&gt;📝 互动练习&lt;/h4&gt;
  &lt;p&gt;这是一个HTML代码示例&lt;/p&gt;
  &lt;button onclick="alert('Hello!')"&gt;点击测试&lt;/button&gt;
&lt;/div&gt;</code></pre>
  
  <div class="interactive" style="background: #f7fafc; border: 2px dashed #4a5568; padding: 20px; margin: 20px 0; border-radius: 8px;">
    <h4>📝 实际效果展示</h4>
    <p>上面的代码会渲染成这样的效果</p>
    <button onclick="alert('HTML渲染成功！')" style="background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">点击测试</button>
  </div>
</section>
        `.trim())
      }
    }
    
    loadTestContent()
  }, [])

  const handleSave = async (content: string) => {
    setSavedContent(content)
    toast({
      title: "内容已保存",
      description: "HTML内容保存成功"
    })
    console.log('保存的内容:', content)
  }

  const handleAutoSave = (content: string) => {
    console.log('自动保存:', content.substring(0, 100) + '...')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              HTML教程编辑器测试
            </h1>
            <p className="text-gray-600">
              测试HTML代码直接渲染功能 - 支持可视化编辑、HTML源码和实时预览
            </p>
            <div className="flex justify-center gap-2 mt-4">
              <Badge variant="default">✅ HTML支持</Badge>
              <Badge variant="secondary">🎨 样式渲染</Badge>
              <Badge variant="outline">⚡ 实时预览</Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* 编辑器 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                🛠️ HTML教程编辑器
              </CardTitle>
              <CardDescription>
                支持三种编辑模式：可视化编辑、HTML源码、实时预览。你可以直接输入HTML代码并查看渲染效果。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <HTMLTutorialEditor
                initialContent={content}
                onSave={handleSave}
                onAutoSave={handleAutoSave}
                placeholder="在这里输入或编辑HTML内容..."
                className="min-h-[600px]"
              />
              
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 使用说明</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>可视化模式</strong>：使用富文本编辑器，类似Word文档编辑</li>
                  <li>• <strong>HTML源码模式</strong>：直接编辑HTML代码，支持模板插入</li>
                  <li>• <strong>预览模式</strong>：查看HTML渲染后的最终效果</li>
                  <li>• <strong>自动保存</strong>：编辑内容会自动保存到本地</li>
                  <li>• <strong>快捷键</strong>：Ctrl+S 手动保存</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* 保存的内容预览 */}
          {savedContent && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  👁️ 最后保存的内容预览
                </CardTitle>
                <CardDescription>
                  这是最后一次手动保存的HTML内容的渲染效果
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ErrorBoundary
                  onError={(error) => {
                    console.error('测试页面内容渲染错误:', error)
                    toast({
                      title: "内容渲染错误", 
                      description: "测试内容格式有问题",
                      variant: "destructive"
                    })
                  }}
                >
                  <div 
                    className="tutorial-content prose max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100"
                    dangerouslySetInnerHTML={{ __html: savedContent }}
                  />
                </ErrorBoundary>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}