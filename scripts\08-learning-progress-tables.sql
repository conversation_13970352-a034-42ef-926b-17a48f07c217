-- ==========================================
-- 用户学习进度系统核心数据表
-- Phase 1: 基础进度跟踪功能
-- ==========================================

-- 教程章节表 (扩展现有教程结构)
CREATE TABLE IF NOT EXISTS tutorial_sections (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  parent_section_id INTEGER REFERENCES tutorial_sections(id),
  title VARCHAR(255) NOT NULL,
  content TEXT,
  section_type VARCHAR(20) DEFAULT 'content' CHECK (section_type IN ('content', 'exercise', 'quiz', 'video')),
  order_index INTEGER NOT NULL,
  estimated_reading_time INTEGER DEFAULT 0,
  difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
  is_required BOOLEAN DEFAULT true,
  unlock_conditions JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户学习记录表
CREATE TABLE IF NOT EXISTS user_learning_records (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  section_id INTEGER REFERENCES tutorial_sections(id) ON DELETE SET NULL,
  
  -- 进度状态
  status VARCHAR(20) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped', 'paused')),
  progress_percentage DECIMAL(5,2) DEFAULT 0 CHECK (progress_percentage BETWEEN 0 AND 100),
  
  -- 时间追踪
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  total_time_spent INTEGER DEFAULT 0,
  active_time_spent INTEGER DEFAULT 0,
  
  -- 学习行为
  scroll_percentage DECIMAL(5,2) DEFAULT 0,
  interaction_count INTEGER DEFAULT 0,
  pause_count INTEGER DEFAULT 0,
  
  -- 元数据
  device_type VARCHAR(20),
  learning_session_id VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE(user_identifier, tutorial_id, section_id)
);

-- 用户学习统计表
CREATE TABLE IF NOT EXISTS user_learning_stats (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  
  -- 基础统计
  total_tutorials_unlocked INTEGER DEFAULT 0,
  total_tutorials_started INTEGER DEFAULT 0,
  total_tutorials_completed INTEGER DEFAULT 0,
  total_sections_completed INTEGER DEFAULT 0,
  
  -- 时间统计
  total_learning_time INTEGER DEFAULT 0,
  average_session_time INTEGER DEFAULT 0,
  learning_streak_days INTEGER DEFAULT 0,
  max_learning_streak INTEGER DEFAULT 0,
  
  -- 学习效率
  completion_rate DECIMAL(5,2) DEFAULT 0,
  average_progress_speed DECIMAL(8,2) DEFAULT 0,
  
  -- 学习偏好
  preferred_learning_time VARCHAR(20),
  preferred_session_length INTEGER DEFAULT 30,
  learning_style JSONB DEFAULT '{}',
  
  last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE(user_identifier)
);

-- 学习成就定义表
CREATE TABLE IF NOT EXISTS learning_achievements (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(50) NOT NULL,
  
  icon_url TEXT,
  badge_color VARCHAR(7) DEFAULT '#3b82f6',
  rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  points INTEGER DEFAULT 10,
  
  unlock_conditions JSONB NOT NULL,
  unlock_message TEXT,
  
  is_active BOOLEAN DEFAULT true,
  is_hidden BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户成就记录表
CREATE TABLE IF NOT EXISTS user_achievements (
  id SERIAL PRIMARY KEY,
  user_identifier VARCHAR(100) NOT NULL,
  achievement_id INTEGER NOT NULL REFERENCES learning_achievements(id) ON DELETE CASCADE,
  
  unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  progress_data JSONB DEFAULT '{}',
  
  is_public BOOLEAN DEFAULT true,
  shared_at TIMESTAMP,
  
  UNIQUE(user_identifier, achievement_id)
);

-- 学习会话表
CREATE TABLE IF NOT EXISTS learning_sessions (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(50) NOT NULL UNIQUE,
  user_identifier VARCHAR(100) NOT NULL,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ended_at TIMESTAMP,
  duration INTEGER,
  is_active BOOLEAN DEFAULT true,
  
  device_type VARCHAR(20),
  user_agent TEXT,
  ip_address INET,
  
  interaction_events JSONB DEFAULT '[]',
  performance_metrics JSONB DEFAULT '{}',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_tutorial_sections_tutorial_id ON tutorial_sections(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_tutorial_sections_order ON tutorial_sections(tutorial_id, order_index);

CREATE INDEX IF NOT EXISTS idx_learning_records_user ON user_learning_records(user_identifier);
CREATE INDEX IF NOT EXISTS idx_learning_records_tutorial ON user_learning_records(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_learning_records_status ON user_learning_records(status);
CREATE INDEX IF NOT EXISTS idx_learning_records_last_accessed ON user_learning_records(last_accessed_at);

CREATE INDEX IF NOT EXISTS idx_learning_stats_user ON user_learning_stats(user_identifier);

CREATE INDEX IF NOT EXISTS idx_achievements_category ON learning_achievements(category);
CREATE INDEX IF NOT EXISTS idx_achievements_active ON learning_achievements(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_identifier);
CREATE INDEX IF NOT EXISTS idx_user_achievements_unlocked ON user_achievements(unlocked_at);

CREATE INDEX IF NOT EXISTS idx_sessions_user ON learning_sessions(user_identifier);
CREATE INDEX IF NOT EXISTS idx_sessions_tutorial ON learning_sessions(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_sessions_started ON learning_sessions(started_at);

-- 创建自动更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_learning_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tutorial_sections_updated_at 
  BEFORE UPDATE ON tutorial_sections 
  FOR EACH ROW EXECUTE PROCEDURE update_learning_updated_at();

CREATE TRIGGER update_learning_records_updated_at 
  BEFORE UPDATE ON user_learning_records 
  FOR EACH ROW EXECUTE PROCEDURE update_learning_updated_at();

CREATE TRIGGER update_learning_stats_updated_at 
  BEFORE UPDATE ON user_learning_stats 
  FOR EACH ROW EXECUTE PROCEDURE update_learning_updated_at();

-- 插入基础成就数据
INSERT INTO learning_achievements (name, display_name, description, category, unlock_conditions, points) VALUES
('first_unlock', '解锁达人', '解锁第一个教程', 'progress', '{"tutorials_unlocked": 1}', 10),
('first_complete', '初学者', '完成第一个教程', 'progress', '{"tutorials_completed": 1}', 25),
('speed_learner', '学习达人', '在一天内完成3个教程', 'efficiency', '{"daily_completions": 3}', 50),
('night_owl', '夜猫子', '在晚上11点后学习超过2小时', 'time', '{"night_learning_minutes": 120}', 30),
('consistent_learner', '坚持不懈', '连续学习7天', 'streak', '{"learning_streak_days": 7}', 100),
('tutorial_master', '教程大师', '完成10个教程', 'progress', '{"tutorials_completed": 10}', 200)
ON CONFLICT (name) DO NOTHING;

-- 创建学习统计更新函数
CREATE OR REPLACE FUNCTION update_user_learning_stats(p_user_identifier VARCHAR)
RETURNS void AS $$
DECLARE
  stats_record RECORD;
BEGIN
  -- 计算用户学习统计
  SELECT 
    COUNT(DISTINCT tutorial_id) as unlocked_count,
    COUNT(DISTINCT CASE WHEN status != 'not_started' THEN tutorial_id END) as started_count,
    COUNT(DISTINCT CASE WHEN status = 'completed' THEN tutorial_id END) as completed_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as sections_completed,
    COALESCE(SUM(total_time_spent), 0) as total_time,
    COALESCE(AVG(total_time_spent), 0) as avg_session,
    ROUND(100.0 * COUNT(CASE WHEN status = 'completed' THEN 1 END) / 
          NULLIF(COUNT(*), 0), 2) as completion_rate
  INTO stats_record
  FROM user_learning_records 
  WHERE user_identifier = p_user_identifier;

  -- 更新或插入统计记录
  INSERT INTO user_learning_stats (
    user_identifier,
    total_tutorials_unlocked,
    total_tutorials_started,
    total_tutorials_completed,
    total_sections_completed,
    total_learning_time,
    average_session_time,
    completion_rate,
    last_calculated_at
  ) VALUES (
    p_user_identifier,
    stats_record.unlocked_count,
    stats_record.started_count,
    stats_record.completed_count,
    stats_record.sections_completed,
    stats_record.total_time,
    stats_record.avg_session,
    stats_record.completion_rate,
    CURRENT_TIMESTAMP
  )
  ON CONFLICT (user_identifier) DO UPDATE SET
    total_tutorials_unlocked = EXCLUDED.total_tutorials_unlocked,
    total_tutorials_started = EXCLUDED.total_tutorials_started,
    total_tutorials_completed = EXCLUDED.total_tutorials_completed,
    total_sections_completed = EXCLUDED.total_sections_completed,
    total_learning_time = EXCLUDED.total_learning_time,
    average_session_time = EXCLUDED.average_session_time,
    completion_rate = EXCLUDED.completion_rate,
    last_calculated_at = EXCLUDED.last_calculated_at,
    updated_at = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 创建学习分析视图
CREATE OR REPLACE VIEW learning_analytics_summary AS
SELECT 
  DATE(created_at) as learning_date,
  user_identifier,
  tutorial_id,
  COUNT(DISTINCT CASE WHEN section_id IS NOT NULL THEN section_id END) as sections_accessed,
  SUM(total_time_spent) as total_time,
  AVG(progress_percentage) as avg_progress,
  COUNT(DISTINCT learning_session_id) as session_count,
  MAX(last_accessed_at) as last_activity
FROM user_learning_records
GROUP BY DATE(created_at), user_identifier, tutorial_id;

-- 查看创建的表
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name IN (
  'tutorial_sections', 
  'user_learning_records', 
  'user_learning_stats',
  'learning_achievements',
  'user_achievements',
  'learning_sessions'
)
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;