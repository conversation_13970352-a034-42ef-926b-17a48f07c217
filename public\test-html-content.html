<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML渲染测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .tutorial-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 { color: #333; }
        .tip {
            background: #e7f3ff;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
        }
        .checkpoint {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .interactive {
            background: #f7fafc;
            border: 2px dashed #4a5568;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="tutorial-content">
        <h1>🎨 UI/UX设计系统完整指南</h1>
        
        <section data-section="intro" data-section-title="课程介绍" data-estimated-time="5">
            <h2>课程介绍</h2>
            <p>欢迎来到UI/UX设计系统完整指南！本课程将带你从零开始构建一个完整的设计系统。</p>
            
            <div class="tip">
                <strong>💡 学习提示：</strong>
                <p>设计系统是现代产品设计的核心，它能确保整个产品的一致性和可扩展性。</p>
            </div>
        </section>

        <section data-section="1" data-section-title="设计原则" data-estimated-time="15">
            <h2>第一章：设计原则</h2>
            <p>设计原则是设计系统的基础，它们指导我们做出一致的设计决策。</p>
            
            <h3>核心设计原则</h3>
            <ul>
                <li><strong>一致性</strong>：保持视觉和交互的一致性</li>
                <li><strong>可访问性</strong>：确保所有用户都能使用</li>
                <li><strong>简洁性</strong>：简化用户界面和交互</li>
                <li><strong>灵活性</strong>：适应不同场景和需求</li>
            </ul>

            <div class="checkpoint" data-checkpoint="1-1" data-checkpoint-type="knowledge" data-points="15">
                <h4>🎯 检查点：设计原则理解</h4>
                <p>请思考：为什么一致性对设计系统如此重要？请举例说明。</p>
            </div>
        </section>

        <section data-section="2" data-section-title="颜色系统" data-estimated-time="20">
            <h2>第二章：颜色系统</h2>
            <p>颜色是设计系统中最重要的视觉元素之一，它传达品牌个性并指导用户行为。</p>
            
            <h3>主色彩搭配</h3>
            <div style="display: flex; gap: 10px; margin: 20px 0;">
                <div style="width: 50px; height: 50px; background: #3b82f6; border-radius: 8px;"></div>
                <div style="width: 50px; height: 50px; background: #10b981; border-radius: 8px;"></div>
                <div style="width: 50px; height: 50px; background: #f59e0b; border-radius: 8px;"></div>
                <div style="width: 50px; height: 50px; background: #ef4444; border-radius: 8px;"></div>
            </div>

            <div class="warning">
                <strong>⚠️ 注意：</strong>
                <p>在选择颜色时，务必考虑可访问性。确保文字和背景之间有足够的对比度。</p>
            </div>

            <div class="interactive" data-interactive="exercise" data-required="true">
                <h4>📝 互动练习：颜色搭配</h4>
                <p>尝试为一个电商网站设计主色彩方案，考虑品牌调性和用户心理。</p>
                <input type="text" placeholder="在这里输入你的颜色搭配方案..." style="width: 100%; padding: 10px; margin-top: 10px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
        </section>

        <section data-section="3" data-section-title="组件库" data-estimated-time="25">
            <h2>第三章：组件库建设</h2>
            <p>组件库是设计系统的具体实现，包含可复用的UI组件。</p>
            
            <h3>常用组件类型</h3>
            <ol>
                <li><strong>基础组件</strong>：按钮、输入框、标签等</li>
                <li><strong>布局组件</strong>：网格、容器、分割线等</li>
                <li><strong>导航组件</strong>：菜单、面包屑、分页等</li>
                <li><strong>反馈组件</strong>：提示、加载、对话框等</li>
            </ol>

            <h3>代码示例</h3>
            <pre><code class="language-css">
/* 按钮组件样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}
            </code></pre>

            <div class="checkpoint" data-checkpoint="3-1" data-checkpoint-type="practice" data-points="20">
                <h4>🎯 检查点：组件设计</h4>
                <p>请设计一个符合无障碍标准的按钮组件，包含正常、悬停、禁用三种状态。</p>
            </div>
        </section>

        <section data-section="4" data-section-title="响应式设计" data-estimated-time="20">
            <h2>第四章：响应式设计规范</h2>
            <p>现代设计系统必须支持多种设备和屏幕尺寸。</p>
            
            <h3>断点系统</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 12px; border: 1px solid #dee2e6;">设备类型</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6;">屏幕宽度</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6;">断点</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">手机</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">< 768px</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">sm</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">平板</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">768px - 1024px</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">md</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">桌面</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">> 1024px</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">lg</td>
                    </tr>
                </tbody>
            </table>

            <div class="tip">
                <strong>💡 最佳实践：</strong>
                <p>采用移动优先的设计策略，从小屏幕开始设计，逐步扩展到大屏幕。</p>
            </div>
        </section>

        <section data-section="5" data-section-title="文档与维护" data-estimated-time="15">
            <h2>第五章：文档与维护</h2>
            <p>良好的文档是设计系统成功的关键，它帮助团队成员理解和正确使用系统。</p>
            
            <h3>文档内容结构</h3>
            <ul>
                <li>🎯 <strong>设计原则</strong>：核心理念和指导思想</li>
                <li>🎨 <strong>视觉规范</strong>：颜色、字体、间距等</li>
                <li>🧩 <strong>组件文档</strong>：使用方法和代码示例</li>
                <li>📋 <strong>模板库</strong>：常用页面模板</li>
                <li>🔄 <strong>更新日志</strong>：版本变更记录</li>
            </ul>

            <div class="interactive" data-interactive="reflection" data-required="false">
                <h4>🤔 课程反思</h4>
                <p>通过本课程的学习，你认为设计系统对团队协作最大的价值是什么？</p>
                <textarea placeholder="在这里分享你的思考..." style="width: 100%; height: 100px; padding: 10px; margin-top: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
            </div>
        </section>

        <section data-section="conclusion" data-section-title="课程总结" data-estimated-time="5">
            <h2>课程总结</h2>
            <p>恭喜你完成了UI/UX设计系统完整指南！</p>
            
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin: 30px 0;">
                <h3 style="color: white; margin-bottom: 15px;">🎉 学习成就</h3>
                <p style="font-size: 18px; margin-bottom: 20px;">你已经掌握了现代设计系统的核心知识！</p>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                    <div>
                        <div style="font-size: 24px;">📐</div>
                        <div>设计原则</div>
                    </div>
                    <div>
                        <div style="font-size: 24px;">🎨</div>
                        <div>颜色系统</div>
                    </div>
                    <div>
                        <div style="font-size: 24px;">🧩</div>
                        <div>组件库</div>
                    </div>
                    <div>
                        <div style="font-size: 24px;">📱</div>
                        <div>响应式设计</div>
                    </div>
                    <div>
                        <div style="font-size: 24px;">📚</div>
                        <div>文档维护</div>
                    </div>
                </div>
            </div>
            
            <h3>下一步建议</h3>
            <ol>
                <li>实践应用：为一个实际项目构建设计系统</li>
                <li>深入学习：探索 Figma、Sketch 等设计工具的高级功能</li>
                <li>团队协作：与开发团队协作，落地设计系统</li>
                <li>持续优化：收集反馈，不断迭代改进</li>
            </ol>
        </section>
    </div>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为检查点添加点击事件
            const checkpoints = document.querySelectorAll('[data-checkpoint]');
            checkpoints.forEach(checkpoint => {
                checkpoint.style.cursor = 'pointer';
                checkpoint.addEventListener('click', function() {
                    this.style.backgroundColor = this.style.backgroundColor === 'rgb(220, 252, 231)' ? '' : '#dcfce7';
                    console.log('检查点已标记:', this.getAttribute('data-checkpoint'));
                });
            });
            
            // 为互动练习添加聚焦效果
            const interactive = document.querySelectorAll('[data-interactive]');
            interactive.forEach(item => {
                const inputs = item.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        item.style.borderColor = '#3b82f6';
                        item.style.backgroundColor = '#eff6ff';
                    });
                    input.addEventListener('blur', function() {
                        item.style.borderColor = '#4a5568';
                        item.style.backgroundColor = '#f7fafc';
                    });
                });
            });
        });
    </script>
</body>
</html>