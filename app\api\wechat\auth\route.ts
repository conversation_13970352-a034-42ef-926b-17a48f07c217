import { NextRequest, NextResponse } from "next/server"

const WECHAT_APPID = process.env.WECHAT_APPID || ""

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const redirectUri = searchParams.get('redirect_uri') || `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/wechat/callback`
    
    // 开发环境下，如果没有配置真实的AppID，返回演示URL
    if (!WECHAT_APPID || WECHAT_APPID === "your_wechat_appid") {
      return NextResponse.json({
        success: true,
        authUrl: "#",  // 演示模式下不跳转
        qrUrl: null,
        state: "demo_state",
        isDemo: true,
        message: "演示模式：点击模拟扫码登录按钮"
      })
    }

    // 生成随机state用于防CSRF攻击
    const state = Math.random().toString(36).substring(2) + Date.now().toString(36)
    
    // 构建微信授权URL
    const authUrl = `https://open.weixin.qq.com/connect/qrconnect?appid=${WECHAT_APPID}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=snsapi_login&state=${state}#wechat_redirect`
    
    // 内嵌二维码URL（可选）
    const qrUrl = `https://open.weixin.qq.com/connect/qrconnect?appid=${WECHAT_APPID}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=snsapi_login&state=${state}&qr=1`

    return NextResponse.json({
      success: true,
      authUrl,
      qrUrl,
      state,
      isDemo: false
    })

  } catch (error) {
    console.error("生成微信授权URL错误:", error)
    return NextResponse.json({ 
      success: false, 
      error: "生成授权链接失败" 
    }, { status: 500 })
  }
}