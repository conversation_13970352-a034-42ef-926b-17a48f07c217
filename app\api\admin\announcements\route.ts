import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

// 获取所有公告（管理员用）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    const { data: announcements, error, count } = await supabaseAdmin
      .from('announcements')
      .select('*', { count: 'exact' })
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("获取公告列表错误:", error)
      return NextResponse.json({ error: "获取公告列表失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: announcements,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })

  } catch (error) {
    console.error("公告API异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}

// 创建新公告
export async function POST(request: NextRequest) {
  try {
    const {
      title,
      content,
      type = 'info',
      priority = 0,
      is_active = true,
      start_date,
      end_date,
      target_audience = 'all'
    } = await request.json()

    // 验证必填字段
    if (!title || !content) {
      return NextResponse.json(
        { error: "标题和内容是必填字段" }, 
        { status: 400 }
      )
    }

    // 验证枚举值
    const validTypes = ['info', 'warning', 'success', 'error']
    const validAudiences = ['all', 'users', 'admins']
    
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: "无效的公告类型" }, 
        { status: 400 }
      )
    }

    if (!validAudiences.includes(target_audience)) {
      return NextResponse.json(
        { error: "无效的目标受众" }, 
        { status: 400 }
      )
    }

    const { data: announcement, error } = await supabaseAdmin
      .from('announcements')
      .insert({
        title: title.trim(),
        content: content.trim(),
        type,
        priority: Math.max(0, Math.min(100, parseInt(priority))),
        is_active,
        start_date: start_date || new Date().toISOString(),
        end_date: end_date || null,
        target_audience,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error("创建公告错误:", error)
      return NextResponse.json({ error: "创建公告失败" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: announcement,
      message: "公告创建成功"
    })

  } catch (error) {
    console.error("创建公告异常:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}