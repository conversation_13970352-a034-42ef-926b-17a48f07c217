import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: {
    tutorialId: string
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const tutorialId = parseInt(params.tutorialId)
    
    if (!tutorialId) {
      return NextResponse.json({ error: "无效的教程ID" }, { status: 400 })
    }

    // 检查教程是否存在
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "教程不存在" }, { status: 404 })
    }

    // 检查是否有已使用的密钥（防止误删除）
    const { data: usedKeys, error: usedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .eq('status', 'used')

    if (usedKeysError) {
      console.error("检查已使用密钥错误:", usedKeysError)
      return NextResponse.json({ error: "检查密钥状态时出错" }, { status: 500 })
    }

    const usedKeyCount = usedKeys.count || 0

    // 删除所有未使用和已导出的密钥，保留已使用的密钥
    const { data: deletedKeys, error: deleteError } = await supabaseAdmin
      .from('tutorial_keys')
      .delete()
      .eq('tutorial_id', tutorialId)
      .in('status', ['unused', 'exported'])
      .select('*', { count: 'exact' })

    if (deleteError) {
      console.error("删除密钥错误:", deleteError)
      return NextResponse.json({ error: "清除密钥失败" }, { status: 500 })
    }

    const deletedCount = deletedKeys.length || 0

    return NextResponse.json({
      success: true,
      message: `成功清除 ${deletedCount} 个密钥`,
      data: {
        tutorial_id: tutorialId,
        tutorial_title: tutorial.title,
        deleted_count: deletedCount,
        preserved_used_keys: usedKeyCount
      }
    })

  } catch (error) {
    console.error("清除密钥异常:", error)
    return NextResponse.json({ 
      error: "服务器内部错误" 
    }, { status: 500 })
  }
}