import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: { tutorialId: string } }
) {
  try {
    const tutorialId = parseInt(params.tutorialId)
    const { count } = await request.json()

    if (!tutorialId) {
      return NextResponse.json({ error: "Invalid tutorial ID" }, { status: 400 })
    }

    if (!count || count < 1 || count > 1000) {
      return NextResponse.json({ error: "Invalid export count (1-1000)" }, { status: 400 })
    }

    // 验证教程是否存在
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 })
    }

    // 获取可用于导出的密钥（未使用且未导出的）
    const { data: availableKeys, error: keysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id, key_code')
      .eq('tutorial_id', tutorialId)
      .eq('status', 'unused')
      .limit(count)
      .order('created_at', { ascending: true })

    if (keysError) {
      console.error("Get available keys error:", keysError)
      return NextResponse.json({ error: "Failed to fetch available keys" }, { status: 500 })
    }

    if (!availableKeys || availableKeys.length === 0) {
      return NextResponse.json({ error: "No available keys to export" }, { status: 400 })
    }

    if (availableKeys.length < count) {
      return NextResponse.json({ 
        error: `Only ${availableKeys.length} keys available, requested ${count}` 
      }, { status: 400 })
    }

    // 将这些密钥标记为已导出
    const keyIds = availableKeys.map(key => key.id)
    const { error: updateError } = await supabaseAdmin
      .from('tutorial_keys')
      .update({ 
        status: 'exported',
        exported_at: new Date().toISOString()
      })
      .in('id', keyIds)

    if (updateError) {
      console.error("Update keys status error:", updateError)
      return NextResponse.json({ error: "Failed to mark keys as exported" }, { status: 500 })
    }

    // 准备导出数据
    const exportData = availableKeys.map(key => key.key_code).join('\n')
    const exportCount = availableKeys.length

    return NextResponse.json({
      success: true,
      data: {
        keys: exportData,
        count: exportCount,
        tutorial_title: tutorial.title,
        export_time: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error("Export keys error:", error)
    return NextResponse.json({ error: "Failed to export keys" }, { status: 500 })
  }
}