"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Key, BookOpen, BarChart3, Settings, Eye, Edit, Trash2, Archive, ArchiveRestore, Bell, Megaphone } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { TutorialKeysManager } from "@/components/admin/TutorialKeysManager"

interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_id: number
  category_name: string
  tags: string[]
  status: string
  price: number
  created_at: string
}

interface Category {
  id: number
  name: string
  description: string
}

interface Stats {
  total_tutorials: number
  total_keys: number
  used_keys: number
  total_unlocks: number
  total_revenue: number
}

interface Announcement {
  id: number
  title: string
  content: string
  type: 'info' | 'warning' | 'success' | 'error'
  priority: number
  is_active: boolean
  start_date: string
  end_date: string | null
  target_audience: 'all' | 'users' | 'admins'
  created_at: string
  updated_at: string
}

export default function AdminDashboard() {
  const router = useRouter()
  // 直接设置为已认证状态，跳过登录
  const [isAuthenticated, setIsAuthenticated] = useState(true)
  const [password, setPassword] = useState("")
  const [tutorials, setTutorials] = useState<Tutorial[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [stats, setStats] = useState<Stats>({
    total_tutorials: 0,
    total_keys: 0,
    used_keys: 0,
    total_unlocks: 0,
    total_revenue: 0,
  })
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // 直接加载数据，不需要等待认证
    loadData()
  }, [])

  const handleLogin = async () => {
    // 临时跳过登录验证
    setIsAuthenticated(true)
    toast({ title: "登录成功", description: "欢迎使用管理后台" })
  }

  const loadData = async () => {
    setLoading(true)
    try {
      // 加载教程列表
      const tutorialsRes = await fetch("/api/admin/tutorials")
      if (tutorialsRes.ok) {
        const tutorialsData = await tutorialsRes.json()
        // 处理新的API响应格式
        setTutorials(tutorialsData.data || tutorialsData)
      }

      // 加载分类列表
      const categoriesRes = await fetch("/api/admin/categories")
      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData)
      }

      // 加载统计数据
      const statsRes = await fetch("/api/admin/stats")
      if (statsRes.ok) {
        const statsData = await statsRes.json()
        setStats(statsData)
      }

      // 加载公告列表
      const announcementsRes = await fetch("/api/admin/announcements")
      if (announcementsRes.ok) {
        const announcementsData = await announcementsRes.json()
        setAnnouncements(announcementsData.data || [])
      }
    } catch (error) {
      console.error("Data loading error:", error)
      // 如果API调用失败，设置一些示例数据以便查看界面
      setTutorials([
        {
          id: 1,
          title: "Next.js 全栈开发教程",
          description: "从零开始学习 Next.js 全栈开发",
          content: "<h1>示例教程内容</h1>",
          category_id: 1,
          category_name: "编程开发",
          tags: ["Next.js", "React", "全栈开发"],
          status: "published",
          price: 99.0,
          created_at: "2024-01-01T00:00:00Z",
        },
      ])
      setCategories([
        { id: 1, name: "编程开发", description: "编程相关的教程内容" },
        { id: 2, name: "设计创意", description: "设计和创意相关的教程" },
      ])
      setStats({
        total_tutorials: 1,
        total_keys: 1,
        used_keys: 0,
        total_unlocks: 0,
      })
    } finally {
      setLoading(false)
    }
  }

  // 公告管理操作函数
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    content: '',
    type: 'info' as const,
    priority: 50,
    is_active: true,
    start_date: new Date().toISOString().slice(0, 16),
    end_date: '',
    target_audience: 'all' as const
  })
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null)
  const [isAnnouncementDialogOpen, setIsAnnouncementDialogOpen] = useState(false)

  const handleCreateAnnouncement = async () => {
    try {
      const response = await fetch('/api/admin/announcements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newAnnouncement)
      })

      if (response.ok) {
        toast({ title: "创建成功", description: "公告已创建" })
        setNewAnnouncement({
          title: '',
          content: '',
          type: 'info',
          priority: 50,
          is_active: true,
          start_date: new Date().toISOString().slice(0, 16),
          end_date: '',
          target_audience: 'all'
        })
        setIsAnnouncementDialogOpen(false)
        loadData()
      } else {
        const error = await response.json()
        toast({ 
          title: "创建失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Create announcement error:', error)
      toast({ title: "创建错误", description: "请检查网络连接", variant: "destructive" })
    }
  }

  const handleUpdateAnnouncement = async () => {
    if (!editingAnnouncement) return

    try {
      const response = await fetch(`/api/admin/announcements/${editingAnnouncement.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingAnnouncement)
      })

      if (response.ok) {
        toast({ title: "更新成功", description: "公告已更新" })
        setEditingAnnouncement(null)
        setIsAnnouncementDialogOpen(false)
        loadData()
      } else {
        const error = await response.json()
        toast({ 
          title: "更新失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Update announcement error:', error)
      toast({ title: "更新错误", description: "请检查网络连接", variant: "destructive" })
    }
  }

  const handleDeleteAnnouncement = async (announcementId: number, title: string) => {
    const confirmed = window.confirm(`确定要删除公告 "${title}" 吗？此操作不可恢复。`)
    if (!confirmed) return

    try {
      const response = await fetch(`/api/admin/announcements/${announcementId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({ title: "删除成功", description: "公告已删除" })
        loadData()
      } else {
        const error = await response.json()
        toast({ 
          title: "删除失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Delete announcement error:', error)
      toast({ title: "删除错误", description: "请检查网络连接", variant: "destructive" })
    }
  }

  const openCreateAnnouncementDialog = () => {
    setEditingAnnouncement(null)
    setNewAnnouncement({
      title: '',
      content: '',
      type: 'info',
      priority: 50,
      is_active: true,
      start_date: new Date().toISOString().slice(0, 16),
      end_date: '',
      target_audience: 'all'
    })
    setIsAnnouncementDialogOpen(true)
  }

  const openEditAnnouncementDialog = (announcement: Announcement) => {
    setEditingAnnouncement({
      ...announcement,
      start_date: announcement.start_date ? new Date(announcement.start_date).toISOString().slice(0, 16) : '',
      end_date: announcement.end_date ? new Date(announcement.end_date).toISOString().slice(0, 16) : ''
    })
    setIsAnnouncementDialogOpen(true)
  }

  // 教程操作函数
  const handlePreviewTutorial = (tutorialId: number) => {
    // 跳转到教程详情页面
    window.open(`/tutorial/${tutorialId}`, '_blank')
  }

  const handleEditTutorial = (tutorialId: number) => {
    // 跳转到编辑页面，通过URL参数传递教程ID
    router.push(`/admin/create-tutorial?edit=${tutorialId}`)
  }

  const checkTutorialDependencies = async (tutorialId: number) => {
    try {
      console.log('🔍 检查教程依赖关系:', tutorialId)
      
      // 获取教程的依赖信息
      const [keysRes, unlocksRes] = await Promise.all([
        fetch(`/api/admin/keys?tutorialId=${tutorialId}`),
        fetch(`/api/user-unlocks?tutorialId=${tutorialId}`)
      ])
      
      console.log('📊 API响应状态:', {
        keysStatus: keysRes.ok,
        unlocksStatus: unlocksRes.ok,
        keysUrl: `/api/admin/keys?tutorialId=${tutorialId}`,
        unlocksUrl: `/api/user-unlocks?tutorialId=${tutorialId}`
      })
      
      let keyCount = 0
      let unlockCount = 0
      
      if (keysRes.ok) {
        const keysData = await keysRes.json()
        // 修复：密钥API返回的data是数组，直接计算长度
        keyCount = Array.isArray(keysData.data) ? keysData.data.length : 0
        console.log('🔑 密钥数据:', { keyCount, data: keysData.data })
      } else {
        console.error('❌ 密钥查询失败:', await keysRes.text())
      }
      
      if (unlocksRes.ok) {
        const unlocksData = await unlocksRes.json()
        // 修复：解锁API在特定查询时直接返回data数组
        unlockCount = Array.isArray(unlocksData.data) ? unlocksData.data.length : 0
        console.log('🔓 解锁数据:', { unlockCount, data: unlocksData.data })
      } else {
        console.error('❌ 解锁查询失败:', await unlocksRes.text())
      }
      
      console.log('📋 依赖关系检查结果:', { tutorialId, keyCount, unlockCount })
      
      return { keyCount, unlockCount }
    } catch (error) {
      console.error('❌ 检查依赖关系失败:', error)
      return { keyCount: 0, unlockCount: 0 }
    }
  }

  const handleDeleteTutorial = async (tutorialId: number, tutorialTitle: string) => {
    try {
      // 首先检查教程的依赖关系
      const { keyCount, unlockCount } = await checkTutorialDependencies(tutorialId)
      
      let deleteMode = 'archive' // 默认为下架模式
      let confirmMessage = `确定要下架教程 "${tutorialTitle}" 吗？`
      let warningMessage = ''
      
      // 根据依赖关系推荐删除策略
      if (unlockCount > 0 && keyCount > 0) {
        deleteMode = 'archive'
        warningMessage = `该教程有 ${keyCount} 个密钥和 ${unlockCount} 个用户解锁记录。\n推荐下架处理以保护用户数据。`
        confirmMessage = `确定要删除教程 "${tutorialTitle}" 吗？\n\n${warningMessage}\n\n下一步将询问删除方式（下架或彻底删除）。`
      } else if (unlockCount > 0) {
        deleteMode = 'archive'
        warningMessage = `该教程有 ${unlockCount} 个用户解锁记录。`
        confirmMessage = `确定要删除教程 "${tutorialTitle}" 吗？\n\n${warningMessage}\n\n下一步将询问删除方式（下架或彻底删除）。`
      } else if (keyCount > 0) {
        deleteMode = 'archive'
        warningMessage = `该教程有 ${keyCount} 个密钥。`
        confirmMessage = `确定要删除教程 "${tutorialTitle}" 吗？\n\n${warningMessage}\n\n下一步将询问删除方式（下架或彻底删除）。`
      } else {
        // 没有任何依赖关系，也默认下架
        confirmMessage = `确定要删除教程 "${tutorialTitle}" 吗？\n\n下一步将询问删除方式（下架或彻底删除）。`
      }
      
      // 显示确认对话框
      const confirmed = window.confirm(confirmMessage)
      if (!confirmed) return
      
      // 询问是否要强制删除（所有情况下都提供选择）
      let finalDeleteMode = deleteMode
      const forceDelete = window.confirm(
        `是否要强制彻底删除？\n\n⚠️ 警告：强制删除将永久删除教程及所有相关数据，包括：\n- ${keyCount} 个密钥\n- ${unlockCount} 个用户解锁记录\n- 所有学习进度数据\n\n此操作不可恢复！\n\n点击"确定"进行强制删除，点击"取消"进行下架操作。`
      )
      if (forceDelete) {
        finalDeleteMode = 'force'
      }

      const response = await fetch(`/api/admin/tutorials/${tutorialId}?mode=${finalDeleteMode}`, {
        method: "DELETE",
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "操作成功",
          description: result.message,
        })
        loadData() // 刷新列表
      } else {
        const error = await response.json()
        toast({ 
          title: "操作失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Delete tutorial error:', error)
      toast({ 
        title: "删除错误", 
        description: "请检查网络连接", 
        variant: "destructive" 
      })
    }
  }

  // 下架教程
  const handleArchiveTutorial = async (tutorialId: number, tutorialTitle: string) => {
    try {
      const confirmed = window.confirm(
        `确定要下架教程 "${tutorialTitle}" 吗？\n\n下架后：\n- 教程不再对外显示\n- 已解锁用户仍可正常访问\n- 所有数据都会保留\n- 可随时重新上架`
      )
      
      if (!confirmed) return

      const response = await fetch(`/api/admin/tutorials/${tutorialId}/archive`, {
        method: "PATCH",
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "下架成功",
          description: result.message,
        })
        loadData() // 刷新列表
      } else {
        const error = await response.json()
        toast({ 
          title: "下架失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Archive tutorial error:', error)
      toast({ 
        title: "下架错误", 
        description: "请检查网络连接", 
        variant: "destructive" 
      })
    }
  }

  // 上架教程
  const handlePublishTutorial = async (tutorialId: number, tutorialTitle: string) => {
    try {
      const confirmed = window.confirm(
        `确定要重新上架教程 "${tutorialTitle}" 吗？\n\n上架后教程将重新对外开放购买。`
      )
      
      if (!confirmed) return

      const response = await fetch(`/api/admin/tutorials/${tutorialId}/publish`, {
        method: "PATCH",
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "上架成功",
          description: result.message,
        })
        loadData() // 刷新列表
      } else {
        const error = await response.json()
        toast({ 
          title: "上架失败", 
          description: error.error || "请稍后重试", 
          variant: "destructive" 
        })
      }
    } catch (error) {
      console.error('Publish tutorial error:', error)
      toast({ 
        title: "上架错误", 
        description: "请检查网络连接", 
        variant: "destructive" 
      })
    }
  }

  // 直接显示管理后台界面，不显示登录表单
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-slate-200">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-slate-900">教程管理后台</h1>
              <p className="text-slate-600 mt-1">管理教程商品和密钥销售</p>
              <div className="mt-3">
                <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                  演示模式 - 无需密码验证
                </Badge>
              </div>
            </div>
            <Button variant="outline" asChild className="shrink-0">
              <a href="/">
                <BookOpen className="h-4 w-4 mr-2" />
                返回商城
              </a>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white shadow-sm border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">总教程数</CardTitle>
              <BookOpen className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{stats.total_tutorials}</div>
            </CardContent>
          </Card>
          <Card className="bg-white shadow-sm border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">总密钥数</CardTitle>
              <Key className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{stats.total_keys}</div>
            </CardContent>
          </Card>
          <Card className="bg-white shadow-sm border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">总营收</CardTitle>
              <BarChart3 className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-emerald-600">¥{stats.total_revenue.toFixed(2)}</div>
            </CardContent>
          </Card>
          <Card className="bg-white shadow-sm border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">总解锁次数</CardTitle>
              <Settings className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{stats.total_unlocks}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tutorials" className="space-y-6">
          <TabsList className="bg-white border border-slate-200 shadow-sm">
            <TabsTrigger value="tutorials" className="data-[state=active]:bg-slate-100">教程管理</TabsTrigger>
            <TabsTrigger value="keys" className="data-[state=active]:bg-slate-100">密钥管理</TabsTrigger>
            <TabsTrigger value="announcements" className="data-[state=active]:bg-slate-100">公告管理</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-slate-100">数据分析</TabsTrigger>
          </TabsList>

          <TabsContent value="tutorials" className="space-y-6">
            <Card className="bg-white shadow-sm border-slate-200">
              <CardHeader className="border-b border-slate-100">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div>
                    <CardTitle className="text-slate-900">教程列表</CardTitle>
                    <CardDescription className="text-slate-600">管理所有教程商品</CardDescription>
                  </div>
                  <Button onClick={() => router.push("/admin/create-tutorial")} className="shrink-0">
                    <Plus className="h-4 w-4 mr-2" />
                    新建教程
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-slate-50">
                      <TableRow className="border-slate-200">
                        <TableHead className="text-slate-700 font-medium">标题</TableHead>
                        <TableHead className="text-slate-700 font-medium">分类</TableHead>
                        <TableHead className="text-slate-700 font-medium">状态</TableHead>
                        <TableHead className="text-slate-700 font-medium">价格</TableHead>
                        <TableHead className="text-slate-700 font-medium">创建时间</TableHead>
                        <TableHead className="text-slate-700 font-medium">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                    {tutorials.map((tutorial) => (
                      <TableRow key={tutorial.id} className="border-slate-200 hover:bg-slate-50 transition-colors">
                        <TableCell className="font-medium text-slate-900">{tutorial.title}</TableCell>
                        <TableCell className="text-slate-600">{tutorial.category_name}</TableCell>
                        <TableCell>
                          <Badge variant={tutorial.status === "published" ? "default" : "secondary"}
                                 className={tutorial.status === "published" 
                                   ? "bg-emerald-100 text-emerald-800 border-emerald-200"
                                   : tutorial.status === "archived"
                                     ? "bg-orange-100 text-orange-800 border-orange-200"
                                     : "bg-slate-100 text-slate-800 border-slate-200"}>
                            {tutorial.status === "published"
                              ? "已发布"
                              : tutorial.status === "draft"
                                ? "草稿"
                                : "已下架"}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium text-slate-900">¥{tutorial.price}</TableCell>
                        <TableCell className="text-slate-600">{new Date(tutorial.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handlePreviewTutorial(tutorial.id)}
                              title="预览教程"
                              className="h-8 w-8 p-0 border-slate-200 hover:bg-slate-50"
                            >
                              <Eye className="h-3.5 w-3.5" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleEditTutorial(tutorial.id)}
                              title="编辑教程"
                              className="h-8 w-8 p-0 border-slate-200 hover:bg-slate-50"
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                            {tutorial.status === 'archived' ? (
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handlePublishTutorial(tutorial.id, tutorial.title)}
                                title="重新上架"
                                className="h-8 w-8 p-0 text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                              >
                                <ArchiveRestore className="h-3.5 w-3.5" />
                              </Button>
                            ) : (
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleArchiveTutorial(tutorial.id, tutorial.title)}
                                title="下架教程"
                                className="h-8 w-8 p-0 text-orange-600 border-orange-200 hover:bg-orange-50"
                              >
                                <Archive className="h-3.5 w-3.5" />
                              </Button>
                            )}
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleDeleteTutorial(tutorial.id, tutorial.title)}
                              title="删除教程"
                              className="h-8 w-8 p-0 text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

          <TabsContent value="announcements" className="space-y-6">
            <Card className="bg-white shadow-sm border-slate-200">
              <CardHeader className="border-b border-slate-100">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div>
                    <CardTitle className="text-slate-900">公告管理</CardTitle>
                    <CardDescription className="text-slate-600">管理系统公告和通知</CardDescription>
                  </div>
                  <Button onClick={openCreateAnnouncementDialog} className="shrink-0">
                    <Plus className="h-4 w-4 mr-2" />
                    新建公告
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-slate-50">
                      <TableRow className="border-slate-200">
                        <TableHead className="text-slate-700 font-medium">标题</TableHead>
                        <TableHead className="text-slate-700 font-medium">类型</TableHead>
                        <TableHead className="text-slate-700 font-medium">优先级</TableHead>
                        <TableHead className="text-slate-700 font-medium">目标受众</TableHead>
                        <TableHead className="text-slate-700 font-medium">状态</TableHead>
                        <TableHead className="text-slate-700 font-medium">创建时间</TableHead>
                        <TableHead className="text-slate-700 font-medium">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {announcements.map((announcement) => (
                        <TableRow key={announcement.id} className="border-slate-200 hover:bg-slate-50 transition-colors">
                        <TableCell className="font-medium text-slate-900">{announcement.title}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={
                              announcement.type === 'error' ? 'destructive' :
                              announcement.type === 'warning' ? 'default' :
                              announcement.type === 'success' ? 'default' : 'secondary'
                            }
                            className={
                              announcement.type === 'error' ? 'bg-red-100 text-red-800 border-red-200' :
                              announcement.type === 'warning' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                              announcement.type === 'success' ? 'bg-emerald-100 text-emerald-800 border-emerald-200' : 
                              'bg-blue-100 text-blue-800 border-blue-200'
                            }
                          >
                            {announcement.type === 'info' ? '信息' :
                             announcement.type === 'warning' ? '警告' :
                             announcement.type === 'success' ? '成功' : '错误'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-slate-600">{announcement.priority}</TableCell>
                        <TableCell className="text-slate-600">
                          {announcement.target_audience === 'all' ? '所有用户' :
                           announcement.target_audience === 'users' ? '普通用户' : '管理员'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={announcement.is_active ? "default" : "secondary"}
                                 className={announcement.is_active 
                                   ? "bg-emerald-100 text-emerald-800 border-emerald-200"
                                   : "bg-slate-100 text-slate-800 border-slate-200"}>
                            {announcement.is_active ? "激活" : "停用"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-slate-600">{new Date(announcement.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => openEditAnnouncementDialog(announcement)}
                              title="编辑公告"
                              className="h-8 w-8 p-0 border-slate-200 hover:bg-slate-50"
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleDeleteAnnouncement(announcement.id, announcement.title)}
                              title="删除公告"
                              className="h-8 w-8 p-0 text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            </Card>

            {/* 公告创建/编辑对话框 */}
            <Dialog open={isAnnouncementDialogOpen} onOpenChange={setIsAnnouncementDialogOpen}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingAnnouncement ? '编辑公告' : '新建公告'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingAnnouncement ? '修改公告信息' : '创建新的系统公告'}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">标题 *</Label>
                      <Input
                        id="title"
                        placeholder="输入公告标题"
                        value={editingAnnouncement?.title || newAnnouncement.title}
                        onChange={(e) => {
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, title: e.target.value })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, title: e.target.value })
                          }
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type">类型</Label>
                      <Select
                        value={editingAnnouncement?.type || newAnnouncement.type}
                        onValueChange={(value) => {
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, type: value as any })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, type: value as any })
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="info">信息</SelectItem>
                          <SelectItem value="warning">警告</SelectItem>
                          <SelectItem value="success">成功</SelectItem>
                          <SelectItem value="error">错误</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="priority">优先级 (0-100)</Label>
                      <Input
                        id="priority"
                        type="number"
                        min="0"
                        max="100"
                        value={editingAnnouncement?.priority || newAnnouncement.priority}
                        onChange={(e) => {
                          const priority = parseInt(e.target.value) || 0
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, priority })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, priority })
                          }
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="target">目标受众</Label>
                      <Select
                        value={editingAnnouncement?.target_audience || newAnnouncement.target_audience}
                        onValueChange={(value) => {
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, target_audience: value as any })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, target_audience: value as any })
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">所有用户</SelectItem>
                          <SelectItem value="users">普通用户</SelectItem>
                          <SelectItem value="admins">管理员</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2 flex items-end">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editingAnnouncement?.is_active ?? newAnnouncement.is_active}
                          onChange={(e) => {
                            if (editingAnnouncement) {
                              setEditingAnnouncement({ ...editingAnnouncement, is_active: e.target.checked })
                            } else {
                              setNewAnnouncement({ ...newAnnouncement, is_active: e.target.checked })
                            }
                          }}
                        />
                        <span>立即激活</span>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start_date">开始时间</Label>
                      <Input
                        id="start_date"
                        type="datetime-local"
                        value={editingAnnouncement?.start_date || newAnnouncement.start_date}
                        onChange={(e) => {
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, start_date: e.target.value })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, start_date: e.target.value })
                          }
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end_date">结束时间（可选）</Label>
                      <Input
                        id="end_date"
                        type="datetime-local"
                        value={editingAnnouncement?.end_date || newAnnouncement.end_date}
                        onChange={(e) => {
                          if (editingAnnouncement) {
                            setEditingAnnouncement({ ...editingAnnouncement, end_date: e.target.value })
                          } else {
                            setNewAnnouncement({ ...newAnnouncement, end_date: e.target.value })
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="content">内容 *</Label>
                    <textarea
                      id="content"
                      className="w-full min-h-[120px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="输入公告内容..."
                      value={editingAnnouncement?.content || newAnnouncement.content}
                      onChange={(e) => {
                        if (editingAnnouncement) {
                          setEditingAnnouncement({ ...editingAnnouncement, content: e.target.value })
                        } else {
                          setNewAnnouncement({ ...newAnnouncement, content: e.target.value })
                        }
                      }}
                    />
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAnnouncementDialogOpen(false)}
                    >
                      取消
                    </Button>
                    <Button 
                      onClick={editingAnnouncement ? handleUpdateAnnouncement : handleCreateAnnouncement}
                      disabled={!(editingAnnouncement?.title || newAnnouncement.title) || 
                               !(editingAnnouncement?.content || newAnnouncement.content)}
                    >
                      {editingAnnouncement ? '更新' : '创建'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </TabsContent>

          <TabsContent value="keys" className="space-y-6">
            <Card className="bg-white shadow-sm border-slate-200">
              <CardHeader className="border-b border-slate-100">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div>
                    <CardTitle className="text-slate-900">密钥管理</CardTitle>
                    <CardDescription className="text-slate-600">按教程管理密钥，支持生成和批量导出</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <TutorialKeysManager 
                  tutorials={tutorials} 
                  onDataUpdate={loadData}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card className="bg-white shadow-sm border-slate-200">
              <CardHeader className="border-b border-slate-100">
                <CardTitle className="text-slate-900">数据分析</CardTitle>
                <CardDescription className="text-slate-600">查看系统使用统计</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <div className="mx-auto w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
                    <BarChart3 className="h-12 w-12 text-slate-400" />
                  </div>
                  <h3 className="text-lg font-medium text-slate-900 mb-2">数据分析功能开发中</h3>
                  <p className="text-slate-500 mb-4">我们正在构建强大的数据分析功能，敬请期待</p>
                  <div className="flex flex-wrap justify-center gap-2 text-sm text-slate-400">
                    <span className="px-3 py-1 bg-slate-100 rounded-full">用户行为分析</span>
                    <span className="px-3 py-1 bg-slate-100 rounded-full">收入统计</span>
                    <span className="px-3 py-1 bg-slate-100 rounded-full">教程热度排行</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
