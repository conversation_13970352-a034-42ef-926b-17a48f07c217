import { NextRequest, NextResponse } from "next/server"
import { CacheManager } from "@/lib/cache-manager"

/**
 * 手动缓存失效API
 * POST /api/admin/cache/invalidate
 * 
 * 允许管理员手动触发缓存清理
 * 用于紧急情况或测试目的
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      scope = 'tutorials', // 'tutorials' | 'all' | 'categories' | 'users'
      force = false,
      reason = 'manual_request'
    } = body

    console.log('🔄 收到手动缓存失效请求:', { scope, force, reason })

    let invalidatedItems: string[] = []

    switch (scope) {
      case 'tutorials':
        await CacheManager.invalidateTutorials({ type: 'all' })
        invalidatedItems = ['tutorials:all', 'tutorials:public', 'tutorials:admin', 'tutorials:search', 'tutorials:filter']
        break

      case 'categories':
        // TODO: 实现分类缓存失效
        invalidatedItems = ['categories:all']
        break

      case 'users':
        // TODO: 实现用户缓存失效  
        invalidatedItems = ['user:*:unlocks']
        break

      case 'all':
        await CacheManager.invalidateAll()
        invalidatedItems = ['*:all']
        break

      default:
        throw new Error(`不支持的缓存作用域: ${scope}`)
    }

    const response = {
      success: true,
      message: '缓存失效成功',
      invalidated_items: invalidatedItems,
      scope,
      force,
      reason,
      timestamp: new Date().toISOString(),
      estimated_refresh_time: '立即生效'
    }

    console.log('✅ 手动缓存失效完成:', response)

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'no-cache, must-revalidate',
        'X-Cache-Invalidated': 'true',
        'X-Invalidation-Scope': scope
      }
    })

  } catch (error) {
    console.error('❌ 手动缓存失效失败:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '缓存失效失败',
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}

/**
 * 获取缓存状态信息
 * GET /api/admin/cache/invalidate
 */
export async function GET() {
  try {
    // 这里可以返回缓存状态信息
    const cacheInfo = {
      supported_scopes: ['tutorials', 'categories', 'users', 'all'],
      cache_strategies: ['IMMEDIATE', 'SHORT_TERM', 'MEDIUM_TERM', 'LONG_TERM'],
      current_status: 'active',
      last_invalidation: null, // TODO: 从实际存储中获取
      total_invalidations: 0, // TODO: 从实际存储中获取
      tips: [
        '使用 scope=tutorials 失效教程相关缓存',
        '使用 scope=all 进行全站缓存清理',
        '设置 force=true 进行强制失效'
      ]
    }

    return NextResponse.json({
      success: true,
      data: cacheInfo
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '获取缓存状态失败'
    }, { status: 500 })
  }
}