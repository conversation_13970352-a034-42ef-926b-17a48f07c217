import { supabaseAdmin } from "./supabase"

// 数据库查询辅助函数 (使用 Supabase)
export async function query(sql: string, params?: any[]) {
  try {
    // 注意：这里使用 Supabase 的 rpc 功能来执行原生 SQL
    // 在生产环境中，建议使用 Supabase 的查询构建器
    console.log('Executing SQL:', sql, 'with params:', params)
    
    // 对于简单查询，我们使用 Supabase 的查询构建器
    // 复杂查询可能需要在 Supabase 中创建存储过程
    throw new Error('请使用 Supabase 查询构建器替代原生 SQL')
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  }
}

// 推荐使用 Supabase 查询构建器的辅助函数
export async function getTutorials(status = 'published') {
  const { data, error } = await supabaseAdmin
    .from('tutorials')
    .select(`
      *,
      categories(name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export async function getCategories() {
  const { data, error } = await supabaseAdmin
    .from('categories')
    .select('*')
    .order('name')
  
  if (error) throw error
  return data
}

export async function getTutorialKeys(tutorialId?: number) {
  let query = supabaseAdmin
    .from('tutorial_keys')
    .select(`
      *,
      tutorials(title)
    `)
  
  if (tutorialId) {
    query = query.eq('tutorial_id', tutorialId)
  }
  
  const { data, error } = await query.order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

// 事务辅助函数 (使用 Supabase)
export async function transaction(callback: () => Promise<any>) {
  try {
    // Supabase 自动处理事务
    // 复杂事务可能需要使用存储过程
    const result = await callback()
    return result
  } catch (error) {
    console.error('Transaction error:', error)
    throw error
  }
}
