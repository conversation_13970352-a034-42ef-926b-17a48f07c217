-- ==========================================
-- 教程内容管理系统数据库架构扩展
-- Phase 1: 核心表结构创建
-- ==========================================

-- 内容版本表 - 支持草稿/发布状态管理
CREATE TABLE IF NOT EXISTS tutorial_versions (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  meta_description VARCHAR(160), -- SEO描述
  meta_keywords TEXT[], -- SEO关键词
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
  author_id VARCHAR(100), -- 作者标识
  editor_id VARCHAR(100), -- 编辑者标识
  change_summary TEXT, -- 版本变更说明
  word_count INTEGER DEFAULT 0, -- 字数统计
  reading_time INTEGER DEFAULT 0, -- 预估阅读时间(分钟)
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(tutorial_id, version_number)
);

-- 媒体资源表 - 图片、视频、文档管理
CREATE TABLE IF NOT EXISTS tutorial_media (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL, -- 原始文件名
  file_type VARCHAR(50) NOT NULL, -- image, video, document, audio
  mime_type VARCHAR(100) NOT NULL,
  file_size BIGINT NOT NULL, -- 文件大小(字节)
  width INTEGER, -- 图片/视频宽度
  height INTEGER, -- 图片/视频高度
  duration INTEGER, -- 视频/音频时长(秒)
  storage_path TEXT NOT NULL, -- Supabase Storage路径
  public_url TEXT NOT NULL,
  thumbnail_url TEXT, -- 缩略图URL
  alt_text VARCHAR(255), -- 图片Alt文本(SEO)
  caption TEXT, -- 媒体说明
  usage_count INTEGER DEFAULT 0, -- 使用次数
  uploaded_by VARCHAR(100), -- 上传者
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容模板表 - 教程模板管理
CREATE TABLE IF NOT EXISTS tutorial_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_content TEXT NOT NULL, -- 模板内容(JSON/HTML)
  category VARCHAR(50), -- 模板分类
  tags TEXT[], -- 模板标签
  preview_image TEXT, -- 预览图
  usage_count INTEGER DEFAULT 0, -- 使用次数
  is_active BOOLEAN DEFAULT true,
  created_by VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SEO优化数据表
CREATE TABLE IF NOT EXISTS tutorial_seo (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  slug VARCHAR(255) UNIQUE NOT NULL, -- URL友好标识
  canonical_url TEXT, -- 规范URL
  og_title VARCHAR(60), -- Open Graph标题
  og_description VARCHAR(160), -- Open Graph描述
  og_image TEXT, -- Open Graph图片
  og_type VARCHAR(20) DEFAULT 'article', -- Open Graph类型
  twitter_card VARCHAR(20) DEFAULT 'summary_large_image',
  structured_data JSONB, -- Schema.org结构化数据
  meta_robots VARCHAR(50) DEFAULT 'index,follow',
  sitemap_priority DECIMAL(2,1) DEFAULT 0.5, -- Sitemap优先级
  sitemap_frequency VARCHAR(20) DEFAULT 'weekly',
  focus_keyword VARCHAR(100), -- 主要关键词
  readability_score INTEGER, -- 可读性评分(0-100)
  seo_score INTEGER DEFAULT 0, -- SEO评分(0-100)
  last_analyzed TIMESTAMP, -- 最后分析时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容统计表 - 阅读量、互动数据
CREATE TABLE IF NOT EXISTS tutorial_analytics (
  id SERIAL PRIMARY KEY,
  tutorial_id INTEGER NOT NULL REFERENCES tutorials(id) ON DELETE CASCADE,
  date DATE NOT NULL, -- 统计日期
  view_count INTEGER DEFAULT 0, -- 浏览量
  unique_visitors INTEGER DEFAULT 0, -- 独立访客
  avg_reading_time INTEGER DEFAULT 0, -- 平均阅读时间(秒)
  bounce_rate DECIMAL(5,2) DEFAULT 0, -- 跳出率
  completion_rate DECIMAL(5,2) DEFAULT 0, -- 完成率
  share_count INTEGER DEFAULT 0, -- 分享次数
  bookmark_count INTEGER DEFAULT 0, -- 收藏次数
  comment_count INTEGER DEFAULT 0, -- 评论数
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(tutorial_id, date)
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_tutorial_versions_tutorial_id ON tutorial_versions(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_tutorial_versions_status ON tutorial_versions(status);
CREATE INDEX IF NOT EXISTS idx_tutorial_versions_published ON tutorial_versions(published_at) WHERE status = 'published';

CREATE INDEX IF NOT EXISTS idx_tutorial_media_tutorial_id ON tutorial_media(tutorial_id);
CREATE INDEX IF NOT EXISTS idx_tutorial_media_type ON tutorial_media(file_type);
CREATE INDEX IF NOT EXISTS idx_tutorial_media_created ON tutorial_media(created_at);

CREATE INDEX IF NOT EXISTS idx_tutorial_seo_slug ON tutorial_seo(slug);
CREATE INDEX IF NOT EXISTS idx_tutorial_seo_keyword ON tutorial_seo(focus_keyword);

CREATE INDEX IF NOT EXISTS idx_tutorial_analytics_tutorial_date ON tutorial_analytics(tutorial_id, date);
CREATE INDEX IF NOT EXISTS idx_tutorial_analytics_date ON tutorial_analytics(date);

-- 添加触发器自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tutorial_versions_updated_at BEFORE UPDATE ON tutorial_versions FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_tutorial_media_updated_at BEFORE UPDATE ON tutorial_media FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_tutorial_templates_updated_at BEFORE UPDATE ON tutorial_templates FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_tutorial_seo_updated_at BEFORE UPDATE ON tutorial_seo FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- 查看创建的表结构
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('tutorial_versions', 'tutorial_media', 'tutorial_templates', 'tutorial_seo', 'tutorial_analytics')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;