import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

// ==========================================
// 分类API v2 - 架构师重构版本
// 支持统计信息、缓存优化、标准化响应
// ==========================================

interface CategoryWithStats {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
  tutorial_count: number
  published_count: number
  price_range?: {
    min: number
    max: number
    average: number
  }
}

interface APIResponse<T> {
  success: boolean
  data: T
  metadata?: {
    total_categories: number
    total_tutorials: number
    cache_time: string
  }
  error?: string
}

export async function GET(request: NextRequest) {
  try {
    console.log('📂 分类API查询开始')
    
    // 获取分类基础信息
    const { data: categories, error: categoriesError } = await supabaseAdmin
      .from('categories')
      .select('*')
      .order('name')

    if (categoriesError) {
      console.error('❌ 分类查询错误:', categoriesError)
      throw categoriesError
    }

    // 获取每个分类的详细统计信息
    const categoriesWithStats: CategoryWithStats[] = await Promise.all(
      (categories || []).map(async (category) => {
        // 获取该分类的教程统计
        const { data: tutorialStats, error: statsError } = await supabaseAdmin
          .from('tutorials')
          .select('price, status')
          .eq('category_id', category.id)

        if (statsError) {
          console.warn(`⚠️ 分类 ${category.name} 统计查询失败:`, statsError)
        }

        const allTutorials = tutorialStats || []
        const publishedTutorials = allTutorials.filter(t => t.status === 'published')
        
        // 计算价格范围
        let priceRange = undefined
        if (publishedTutorials.length > 0) {
          const prices = publishedTutorials.map(t => t.price).filter(p => p > 0)
          if (prices.length > 0) {
            priceRange = {
              min: Math.min(...prices),
              max: Math.max(...prices),
              average: Math.round(prices.reduce((a, b) => a + b, 0) / prices.length)
            }
          }
        }

        return {
          id: category.id,
          name: category.name,
          description: category.description,
          created_at: category.created_at,
          updated_at: category.updated_at,
          tutorial_count: allTutorials.length,
          published_count: publishedTutorials.length,
          price_range: priceRange
        }
      })
    )

    // 计算总体统计
    const totalTutorials = categoriesWithStats.reduce((sum, cat) => sum + cat.published_count, 0)

    // 标准化API响应
    const response: APIResponse<CategoryWithStats[]> = {
      success: true,
      data: categoriesWithStats,
      metadata: {
        total_categories: categoriesWithStats.length,
        total_tutorials: totalTutorials,
        cache_time: new Date().toISOString()
      }
    }

    console.log(`✅ 返回 ${categoriesWithStats.length} 个分类, ${totalTutorials} 个教程`)

    // 设置缓存头（10分钟，分类数据变更频率较低）
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=600, stale-while-revalidate=1200',
        'X-Total-Categories': categoriesWithStats.length.toString(),
        'X-Total-Tutorials': totalTutorials.toString()
      }
    })

  } catch (error) {
    console.error('❌ 分类API异常:', error)
    
    const errorResponse: APIResponse<null> = {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : '获取分类列表失败'
    }

    return NextResponse.json(errorResponse, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
